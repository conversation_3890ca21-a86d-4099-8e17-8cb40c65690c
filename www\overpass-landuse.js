// Kleines Modul für Overpass-Landuse-Abfrage
// Nutzt fetch, gibt Promise<string> zurück (z.B. "forest", "residential", ... oder "unbekannt")

// Cache für Overpass API-Anfragen
const overpassCache = new Map();
const CACHE_DURATION_MS = 10 * 60 * 1000; // 10 Minuten
const MAX_CACHE_ENTRIES = 50; // Maximal 50 Cache-Einträge

// Cache-Schlüssel generieren basierend auf Position und Radius
function generateCacheKey(lat, lng, radius) {
  // Runde auf 3 Dezimalstellen (~100m Genauigkeit) für bessere Cache-Hits
  const roundedLat = Math.round(lat * 1000) / 1000;
  const roundedLng = Math.round(lng * 1000) / 1000;
  return `${roundedLat},${roundedLng},${radius}`;
}

// Cache-Eintrag prüfen und ggf. zurückgeben
function getCachedResult(cacheKey) {
  const cached = overpassCache.get(cacheKey);
  if (cached && (Date.now() - cached.timestamp) < CACHE_DURATION_MS) {
    console.log(`[OVERPASS-CACHE] Cache hit for key: ${cacheKey}`);
    return cached.data;
  }
  if (cached) {
    // Abgelaufener Cache-Eintrag entfernen
    overpassCache.delete(cacheKey);
    console.log(`[OVERPASS-CACHE] Expired cache entry removed: ${cacheKey}`);
  }
  return null;
}

// Ergebnis im Cache speichern
function setCachedResult(cacheKey, data) {
  // Cache-Größe begrenzen (LRU-ähnlich: älteste Einträge entfernen)
  if (overpassCache.size >= MAX_CACHE_ENTRIES) {
    const oldestKey = overpassCache.keys().next().value;
    overpassCache.delete(oldestKey);
    console.log(`[OVERPASS-CACHE] Removed oldest cache entry: ${oldestKey}`);
  }

  overpassCache.set(cacheKey, {
    data: data,
    timestamp: Date.now()
  });
  console.log(`[OVERPASS-CACHE] Cached result for key: ${cacheKey}`);
}

// Hilfsfunktion: Punkt-in-Polygon-Test (Raycasting-Algorithmus)
function pointInPolygon(lat, lng, polygon) {
    let inside = false;
    for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
        const xi = polygon[i][0], yi = polygon[i][1];
        const xj = polygon[j][0], yj = polygon[j][1];
        const intersect = ((yi > lng) !== (yj > lng)) &&
            (lat < (xj - xi) * (lng - yi) / (yj - yi + 1e-10) + xi);
        if (intersect) inside = !inside;
    }
    return inside;
}

// Liefert alle Landuse/Natural/Leisure-Polygone im Umkreis als GeoJSON FeatureCollection
export async function getLandusePolygonsGeoJSON(lat, lng, radiusMeters = 500) {
    // Cache-Schlüssel generieren
    const cacheKey = generateCacheKey(lat, lng, radiusMeters);

    // Prüfen ob Ergebnis im Cache vorhanden ist
    const cachedResult = getCachedResult(cacheKey);
    if (cachedResult) {
        return cachedResult;
    }

    console.log(`[OVERPASS-CACHE] Cache miss, making API request for: ${cacheKey}`);

    const query = `[out:json];(
      way["landuse"](around:${radiusMeters},${lat},${lng});
      way["natural"](around:${radiusMeters},${lat},${lng});
      way["leisure"](around:${radiusMeters},${lat},${lng});
    );
    (._;>;);
    out body;`;
    const url = 'https://overpass-api.de/api/interpreter';

    try {
        const startTime = Date.now();
        const response = await fetch(url, {
            method: 'POST',
            body: query,
            headers: { 'Content-Type': 'text/plain' }
        });

        const emptyResult = { type: 'FeatureCollection', features: [] };
        if (!response.ok) {
            console.log(`[OVERPASS-CACHE] API request failed with status: ${response.status}`);
            setCachedResult(cacheKey, emptyResult);
            return emptyResult;
        }

        const data = await response.json();
        const duration = Date.now() - startTime;
        console.log(`[OVERPASS-CACHE] API request completed in ${duration}ms`);

        if (!data.elements || data.elements.length === 0) {
            setCachedResult(cacheKey, emptyResult);
            return emptyResult;
        }
        // Knoten-Id -> Koordinate
        const nodes = {};
        for (const el of data.elements) {
            if (el.type === 'node') {
                nodes[el.id] = [el.lon, el.lat]; // GeoJSON: [lon,lat]
            }
        }
        const features = [];
        for (const el of data.elements) {
            if (el.type === 'way' && el.nodes && el.nodes.length >= 3) {
                let typeTag = el.tags.landuse ? 'landuse' : (el.tags.natural ? 'natural' : (el.tags.leisure ? 'leisure' : null));
                let valueTag = el.tags[typeTag];
                if (!typeTag || !valueTag) continue;
                const coords = el.nodes.map(nid => nodes[nid]).filter(Boolean);
                // Polygon schließen
                if (coords.length >= 3 && (coords[0][0] !== coords[coords.length-1][0] || coords[0][1] !== coords[coords.length-1][1])) {
                    coords.push(coords[0]);
                }
                if (coords.length >= 4) {
                    features.push({
                        type: 'Feature',
                        geometry: {
                            type: 'Polygon',
                            coordinates: [coords]
                        },
                        properties: {
                            osm_id: el.id,
                            type: typeTag,
                            value: valueTag
                        }
                    });
                }
            }
        }

        const result = { type: 'FeatureCollection', features };

        // Ergebnis im Cache speichern
        setCachedResult(cacheKey, result);
        console.log(`[OVERPASS-CACHE] Processed ${features.length} features and cached result`);

        return result;
    } catch (e) {
        console.error(`[OVERPASS-CACHE] API request failed:`, e);
        const emptyResult = { type: 'FeatureCollection', features: [] };
        // Auch Fehler-Ergebnisse cachen (für kürzere Zeit)
        setCachedResult(cacheKey, emptyResult);
        return emptyResult;
    }
}

export async function getLanduseForLatLng(lat, lng) {
    // Cache-Schlüssel generieren (mit fester Radius von 50m)
    const cacheKey = generateCacheKey(lat, lng, 50);

    // Prüfen ob Ergebnis im Cache vorhanden ist
    const cachedResult = getCachedResult(cacheKey);
    if (cachedResult) {
        return cachedResult;
    }

    console.log(`[OVERPASS-CACHE] Cache miss for point query, making API request for: ${cacheKey}`);

    // Overpass QL: hole alle landuse-Polygone im Umkreis von 50m
    const query = `[out:json];(
      way["landuse"](around:50,${lat},${lng});
      way["natural"](around:50,${lat},${lng});
      way["leisure"](around:50,${lat},${lng});
    );
    (._;>;);
    out body;`;
    const url = 'https://overpass-api.de/api/interpreter';

    try {
        const startTime = Date.now();
        const response = await fetch(url, {
            method: 'POST',
            body: query,
            headers: { 'Content-Type': 'text/plain' }
        });

        const unknownResult = {type: null, value: 'unbekannt'};
        if (!response.ok) {
            console.log(`[OVERPASS-CACHE] Point query API request failed with status: ${response.status}`);
            setCachedResult(cacheKey, unknownResult);
            return unknownResult;
        }

        const data = await response.json();
        const duration = Date.now() - startTime;
        console.log(`[OVERPASS-CACHE] Point query API request completed in ${duration}ms`);
        ('[OVERPASS-LANDUSE] Antwort:', data);
        if (!data.elements || data.elements.length === 0) {
            ('[OVERPASS-LANDUSE] Keine Elemente erhalten!');
            setCachedResult(cacheKey, unknownResult);
            return unknownResult;
        }
        // Baue ein Knoten-Id -> Koordinate Mapping
        const nodes = {};
        for (const el of data.elements) {
            if (el.type === 'node') {
                nodes[el.id] = [el.lat, el.lon];
            }
        }
        let found = false;
        let polyCount = 0;
        // Prüfe für jedes Polygon, ob der Punkt enthalten ist
        for (const el of data.elements) {
            // Prüfe alle Polygone (landuse, natural, leisure)
            if (el.type === 'way' && el.tags && el.nodes && el.nodes.length >= 3) {
                // Bestimme den Typ des Polygons (landuse, natural, leisure)
                let typeTag = null;
                let valueTag = null;

                if (el.tags.landuse) {
                    typeTag = 'landuse';
                    valueTag = el.tags.landuse;
                } else if (el.tags.natural) {
                    typeTag = 'natural';
                    valueTag = el.tags.natural;
                } else if (el.tags.leisure) {
                    typeTag = 'leisure';
                    valueTag = el.tags.leisure;
                }

                // Wenn kein Typ gefunden wurde, überspringe dieses Polygon
                if (!typeTag || !valueTag) continue;

                const polygon = el.nodes.map(nid => nodes[nid]).filter(Boolean);
                polyCount++;
                console.log(`[OVERPASS-LANDUSE] Prüfe Polygon: ${typeTag}=${valueTag}`);

                if (polygon.length >= 3) {
                    // Standard-Test (lat,lon)
                    const inside = pointInPolygon(lat, lng, polygon);
                    // Alternativ: Test mit vertauschter Reihenfolge (lon,lat)
                    let insideSwapped = false;
                    if (!inside) {
                        const swappedPoly = polygon.map(([a,b]) => [b,a]);
                        insideSwapped = pointInPolygon(lng, lat, swappedPoly);
                    }

                    console.log(`[OVERPASS-LANDUSE] Punkt-in-Polygon: ${inside} (swapped: ${insideSwapped}) für Typ ${typeTag}=${valueTag}`);

                    if (inside || insideSwapped) {
                        found = true;
                        const result = {type: typeTag, value: valueTag};
                        setCachedResult(cacheKey, result);
                        console.log(`[OVERPASS-CACHE] Found landuse ${typeTag}=${valueTag} and cached result`);
                        return result;
                    }
                }
            }
        }
        (`[OVERPASS-LANDUSE] Anzahl geprüfter Polygone: ${polyCount}`);
        if (!found) {
            ('[OVERPASS-LANDUSE] Kein passendes Polygon gefunden!');
        }

        setCachedResult(cacheKey, unknownResult);
        console.log(`[OVERPASS-CACHE] No landuse found, cached unknown result`);
        return unknownResult;
    } catch (e) {
        console.error(`[OVERPASS-CACHE] Point query failed:`, e);
        setCachedResult(cacheKey, unknownResult);
        return unknownResult;
    }
}

// Cache-Management-Funktionen für Debugging und Wartung
export function getCacheStats() {
    const now = Date.now();
    let validEntries = 0;
    let expiredEntries = 0;

    for (const [key, entry] of overpassCache.entries()) {
        if ((now - entry.timestamp) < CACHE_DURATION_MS) {
            validEntries++;
        } else {
            expiredEntries++;
        }
    }

    return {
        totalEntries: overpassCache.size,
        validEntries,
        expiredEntries,
        maxEntries: MAX_CACHE_ENTRIES,
        cacheDurationMinutes: CACHE_DURATION_MS / (60 * 1000)
    };
}

export function clearCache() {
    const clearedCount = overpassCache.size;
    overpassCache.clear();
    console.log(`[OVERPASS-CACHE] Cleared ${clearedCount} cache entries`);
    return clearedCount;
}

export function cleanupExpiredCache() {
    const now = Date.now();
    let cleanedCount = 0;

    for (const [key, entry] of overpassCache.entries()) {
        if ((now - entry.timestamp) >= CACHE_DURATION_MS) {
            overpassCache.delete(key);
            cleanedCount++;
        }
    }

    if (cleanedCount > 0) {
        console.log(`[OVERPASS-CACHE] Cleaned up ${cleanedCount} expired cache entries`);
    }

    return cleanedCount;
}

