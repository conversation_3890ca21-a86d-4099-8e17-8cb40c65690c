---------------------------- PROCESS STARTED (24859) for package com.example.gpspokemonapp ----------------------------
22:23:46.196 Compatibil...geReporter  D  Compat change id reported: 171979766; UID 10318; state: ENABLED
22:23:46.203 ziparchive               W  Unable to open '/data/app/~~tBaKV3gMkvzMc0G10eCahg==/com.example.gpspokemonapp-62BPVGKeNC3vuORSyCHS6A==/base.dm': No such file or directory
22:23:46.203 ziparchive               W  Unable to open '/data/app/~~tBaKV3gMkvzMc0G10eCahg==/com.example.gpspokemonapp-62BPVGKeNC3vuORSyCHS6A==/base.dm': No such file or directory
22:23:46.270 nativeloader             D  Configuring classloader-namespace for other apk /data/app/~~tBaKV3gMkvzMc0G10eCahg==/com.example.gpspokemonapp-62BPVGKeNC3vuORSyCHS6A==/base.apk. target_sdk_version=35, uses_libraries=, library_path=/data/app/~~tBaKV3gMkvzMc0G10eCahg==/com.example.gpspokemonapp-62BPVGKeNC3vuORSyCHS6A==/lib/arm64, permitted_path=/data:/mnt/expand:/data/user/0/com.example.gpspokemonapp
22:23:46.279 GraphicsEnvironment      V  ANGLE Developer option for 'com.example.gpspokemonapp' set to: 'default'
22:23:46.280 GraphicsEnvironment      V  ANGLE GameManagerService for com.example.gpspokemonapp: false
22:23:46.280 GraphicsEnvironment      V  App is not on the allowlist for updatable production driver.
22:23:46.282 NetworkSecurityConfig    D  No Network Security Config specified, using platform default
22:23:46.289 NetworkSecurityConfig    D  No Network Security Config specified, using platform default
22:23:46.305 OpenGLRenderer           D  RenderThread::requireGlContext()
22:23:46.306 AdrenoGLES-0             I  QUALCOMM build                   : 193b2ee, I593c16c433
Build Date                       : 10/07/21
OpenGL ES Shader Compiler Version: EV031.32.02.10
Local Branch                     : Test-lib-**********
Remote Branch                    : 
Remote Branch                    : 
Reconstruct Branch               : 
22:23:46.306 AdrenoGLES-0             I  Build Config                     : S P 10.0.5 AArch64
22:23:46.306 AdrenoGLES-0             I  Driver Path                      : /vendor/lib64/egl/libGLESv2_adreno.so
22:23:46.309 AdrenoGLES-0             I  PFP: 0x016dd093, ME: 0x00000000
22:23:46.314 OpenGLRenderer           D  RenderThread::setGrContext()
22:23:46.323 AppCompatDelegate        D  Checking for metadata for AppLocalesMetadataHolderService : Service not found
22:23:46.345 DecorView                I  [INFO] isPopOver=false config=true
22:23:46.345 DecorView                I  updateCaptionType: isFloating=false isApplication=true hasWindowDecorCaption=false this=DecorView@64101ce[]
22:23:46.345 DecorView                D  setCaptionType = 0, this = DecorView@64101ce[]
22:23:46.349 DecorView                I  getCurrentDensityDpi: from real metrics. densityDpi=480 msg=resources_loaded
22:23:46.349 DecorView                I  setWindowBackground: isPopOver=false color=ff303030 d=android.graphics.drawable.ColorDrawable@ff7bffc
22:23:46.353 e.gpspokemonapp          W  Accessing hidden method Landroid/view/ViewGroup;->makeOptionalFitsSystemWindows()V (unsupported, reflection, allowed)
22:23:46.358 WebViewFactory           I  Loading com.google.android.webview version 138.0.7204.168 (code 720416833)
22:23:46.361 ziparchive               W  Unable to open '/data/app/~~RsP13BQhrRkcGSGm7byaAQ==/com.google.android.trichromelibrary_720416833-xV8P9c6nnl8BHEFoceUq0w==/base.dm': No such file or directory
22:23:46.361 ziparchive               W  Unable to open '/data/app/~~RsP13BQhrRkcGSGm7byaAQ==/com.google.android.trichromelibrary_720416833-xV8P9c6nnl8BHEFoceUq0w==/base.dm': No such file or directory
22:23:46.361 e.gpspokemonapp          W  Entry not found
22:23:46.361 nativeloader             D  Configuring classloader-namespace for other apk /data/app/~~RsP13BQhrRkcGSGm7byaAQ==/com.google.android.trichromelibrary_720416833-xV8P9c6nnl8BHEFoceUq0w==/base.apk. target_sdk_version=35, uses_libraries=ALL, library_path=/data/app/~~PlMsdVbGaEH-aAVai6xQ1Q==/com.google.android.webview-PNdiXtTRxAs3IBRzJUczYA==/lib/arm64:/data/app/~~PlMsdVbGaEH-aAVai6xQ1Q==/com.google.android.webview-PNdiXtTRxAs3IBRzJUczYA==/base.apk!/lib/arm64-v8a:/data/app/~~RsP13BQhrRkcGSGm7byaAQ==/com.google.android.trichromelibrary_720416833-xV8P9c6nnl8BHEFoceUq0w==/base.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand
22:23:46.361 nativeloader             D  Extending system_exposed_libraries: libhumantracking.arcsoft.so:libPortraitDistortionCorrection.arcsoft.so:libPortraitDistortionCorrectionCali.arcsoft.so:libface_landmark.arcsoft.so:libFacialStickerEngine.arcsoft.so:libveengine.arcsoft.so:lib_pet_detection.arcsoft.so:libhigh_res.arcsoft.so:libimage_enhancement.arcsoft.so:liblow_light_hdr.arcsoft.so:libhigh_dynamic_range.arcsoft.so:libsuperresolution.arcsoft.so:libobjectcapture.arcsoft.so:libobjectcapture_jni.arcsoft.so:libobjectcapture_jni.arcsoft.so:libFacialAttributeDetection.arcsoft.so:libBeauty_v4.camera.samsung.so:libexifa.camera.samsung.so:libjpega.camera.samsung.so:libOpenCv.camera.samsung.so:libVideoClassifier.camera.samsung.so:libtensorflowLite.dynamic_viewing.camera.samsung.so:libImageScreener.camera.samsung.so:libMyFilter.camera.samsung.so:libtflite2.myfilters.camera.samsung.so:libHIDTSnapJNI.camera.samsung.so:libSmartScan.camera.samsung.so:libRectify.camera.samsung.so:libDocRectifyWrapper.camera.samsung.so:libUltraWideDistortionCorrection.camera.
22:23:46.364 nativeloader             D  Configuring classloader-namespace for other apk /data/app/~~PlMsdVbGaEH-aAVai6xQ1Q==/com.google.android.webview-PNdiXtTRxAs3IBRzJUczYA==/base.apk. target_sdk_version=35, uses_libraries=, library_path=/data/app/~~PlMsdVbGaEH-aAVai6xQ1Q==/com.google.android.webview-PNdiXtTRxAs3IBRzJUczYA==/lib/arm64:/data/app/~~PlMsdVbGaEH-aAVai6xQ1Q==/com.google.android.webview-PNdiXtTRxAs3IBRzJUczYA==/base.apk!/lib/arm64-v8a:/data/app/~~RsP13BQhrRkcGSGm7byaAQ==/com.google.android.trichromelibrary_720416833-xV8P9c6nnl8BHEFoceUq0w==/base.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand
22:23:46.379 cr_WVCFactoryProvider    I  version=138.0.7204.168 (720416833) minSdkVersion=29 isBundle=false multiprocess=true packageId=127
22:23:46.383 chromium                 I  [0724/222346.383347:INFO:android_webview/browser/variations/variations_seed_loader.cc:67] Failed to open file for reading.: No such file or directory (2)
22:23:46.392 cr_LibraryLoader         I  Successfully loaded native library
22:23:46.392 cr_CachingUmaRecorder    I  Flushed 8 samples from 8 histograms, 0 samples were dropped.
22:23:46.394 cr_CombinedPProvider     I  #registerProvider() provider:WV.d9@7cd4eb isPolicyCacheEnabled:false policyProvidersSize:0
22:23:46.395 cr_PolicyProvider        I  #setManagerAndSource() 0
22:23:46.408 cr_CombinedPProvider     I  #linkNativeInternal() 1
22:23:46.408 Compatibil...geReporter  D  Compat change id reported: 183155436; UID 10318; state: ENABLED
22:23:46.409 cr_AppResProvider        I  #getApplicationRestrictionsFromUserManager() Bundle[EMPTY_PARCEL]
22:23:46.409 cr_PolicyProvider        I  #notifySettingsAvailable() 0
22:23:46.409 cr_CombinedPProvider     I  #onSettingsAvailable() 0
22:23:46.409 cr_CombinedPProvider     I  #flushPolicies()
22:23:46.425 chromium                 W  [WARNING:net/dns/dns_config_service_android.cc:69] Failed to read DnsConfig.
22:23:46.428 chromium                 W  [WARNING:android_webview/browser/network_service/net_helpers.cc:143] HTTP Cache size is: 20971520
22:23:46.448 Compatibil...geReporter  D  Compat change id reported: 214741472; UID 10318; state: ENABLED
22:23:46.451 Compatibil...geReporter  D  Compat change id reported: 171228096; UID 10318; state: ENABLED
22:23:46.474 Capacitor                D  Starting BridgeActivity
22:23:46.487 Capacitor                D  Registering plugin instance: CapacitorCookies
22:23:46.489 Capacitor                D  Registering plugin instance: WebView
22:23:46.489 Capacitor                D  Registering plugin instance: CapacitorHttp
22:23:46.490 Capacitor                D  Registering plugin instance: KeepAwake
22:23:46.491 Capacitor                D  Registering plugin instance: App
22:23:46.491 Capacitor                D  Registering plugin instance: Geolocation
22:23:46.514 Capacitor                W  Unable to read file at path public/plugins
22:23:46.517 Capacitor                D  Loading app at https://localhost
22:23:46.529 cr_media                 W  BLUETOOTH_CONNECT permission is missing.
22:23:46.530 cr_media                 W  getBluetoothAdapter() requires BLUETOOTH permission
22:23:46.530 cr_media                 W  registerBluetoothIntentsIfNeeded: Requires BLUETOOTH permission
22:23:46.537 Capacitor                D  Handling local request: https://localhost/
22:23:46.539 AdrenoVK-0               I  ===== BEGIN DUMP OF OVERRIDDEN SETTINGS =====
22:23:46.539 AdrenoVK-0               I  ===== END DUMP OF OVERRIDDEN SETTINGS =====
22:23:46.539 AdrenoVK-0               I  QUALCOMM build          : 193b2ee, I593c16c433
Build Date              : 10/07/21
Shader Compiler Version : EV031.32.02.10
Local Branch            : Test-lib-**********
Remote Branch           : 
Remote Branch           : 
Reconstruct Branch      : 
22:23:46.539 AdrenoVK-0               I  Build Config            : S P 10.0.5 AArch64
22:23:46.539 AdrenoVK-0               I  Driver Path             : /vendor/lib64/hw/vulkan.adreno.so
22:23:46.542 Capacitor                D  App started
22:23:46.543 OnBackInvokedCallback    W  OnBackInvokedCallback is not enabled for the application.
Set 'android:enableOnBackInvokedCallback="true"' in the application manifest.
22:23:46.544 Capacitor/AppPlugin      D  Firing change: true
22:23:46.544 Capacitor/AppPlugin      V  Notifying listeners for event appStateChange
22:23:46.544 Capacitor/AppPlugin      D  No listeners found for event appStateChange
22:23:46.545 Capacitor                D  App resumed
22:23:46.546 MSHandlerLifeCycle       I  check: return. pkg=com.example.gpspokemonapp parent=null callers=com.android.internal.policy.DecorView.setVisibility:4412 android.app.ActivityThread.handleResumeActivity:5476 android.app.servertransaction.ResumeActivityItem.execute:54 android.app.servertransaction.ActivityTransactionItem.execute:45 android.app.servertransaction.TransactionExecutor.executeLifecycleState:176 
22:23:46.546 MSHandlerLifeCycle       I  removeMultiSplitHandler: no exist. decor=DecorView@64101ce[]
22:23:46.551 NativeCust...ncyManager  D  [NativeCFMS] BpCustomFrequencyManager::BpCustomFrequencyManager()
22:23:46.558 InsetsController         D  onStateChanged: InsetsState: {mDisplayFrame=Rect(0, 0 - 1080, 2400), mDisplayCutout=DisplayCutout{insets=Rect(0, 88 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 0 - 0, 0), Rect(512, 0 - 568, 88), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2400 physicalDisplayWidth=1080 physicalDisplayHeight=2400 density={3.0} cutoutSpec={M 0,0 M 0,10.71963616907435 a 9.280363830925644,9.280363830925644 0 1,0 0,18.56072766185129 a 9.280363830925644,9.280363830925644 0 1,0 0,-18.56072766185129 Z @dp} rotation={0} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=96, center=Point(96, 96)}, RoundedCorner{position=TopRight, radius=96, center=Point(984, 96)}, RoundedCorner{position=BottomRight, radius=96, center=Point(984, 2304)}, RoundedCorner{position=BottomLeft, radius=96, center=Point(96, 2304)}]}  mRoundedCornerFrame=Rect(0, 0 - 1080, 2400), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(948, 0 - 1080, 88) rotation=0}, mSources= { InsetsSource: {mType=ITYPE_STATUS_BAR, mFrame=[0,0][1080,88], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_NAVIGATION_BAR, mFrame=[0,2256][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_LEFT_GESTURES, mFrame=[0,0][0,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_RIGHT_GESTURES, mFrame=[1080,0][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_TOP_MANDATORY_GESTURES, mFrame=[0,0][1080,124], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_BOTTOM_MANDATORY_GESTURES, mFrame=[0,2256][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_LEFT_DISPLAY_CUTOUT, mFrame=[0,0][-100000,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_TOP_DISPLAY_CUTOUT, mFrame=[0,0][1080,88], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_RIGHT_DISPLAY_CUTOUT, mFrame=[100000,0][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_BOTTOM_DISPLAY_CUTOUT, mFrame=[0,100000][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_TOP_TAPPABLE_ELEMENT, mFrame=[0,0][1080,88], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_BOTTOM_TAPPABLE_ELEMENT, mFrame=[0,2256][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false} } host=com.example.gpspokemonapp/com.example.gpspokemonapp.MainActivity from=android.view.ViewRootImpl.setView:1732
22:23:46.560 ViewRootIm...nActivity]  I  setView = com.android.internal.policy.DecorView@64101ce TM=true
22:23:46.561 MSHandlerLifeCycle       I  removeMultiSplitHandler: no exist. decor=DecorView@64101ce[MainActivity]
22:23:46.572 Choreographer            I  Skipped 32 frames!  The application may be doing too much work on its main thread.
22:23:46.581 Compatibil...geReporter  D  Compat change id reported: 193247900; UID 10318; state: ENABLED
22:23:46.582 CameraManagerGlobal      I  Connecting to camera service
22:23:46.585 VendorTagDescriptor      D  addVendorDescriptor: vendor tag id 14172875900359437128 added
22:23:46.586 ViewRootIm...nActivity]  I  performTraversals params={(0,0)(fillxfill) sim={adjust=resize forwardNavigation} layoutInDisplayCutoutMode=shortEdges ty=BASE_APPLICATION wanim=0x1030309
  fl=81810100
  pfl=12020040
  bhv=DEFAULT
  fitSides= naviIconColor=0}
22:23:46.587 CameraManager            I  registerAvailabilityCallback: Is device callback = false
22:23:46.587 CameraManagerGlobal      I  Camera 0 facing CAMERA_FACING_BACK state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
22:23:46.587 CameraManagerGlobal      I  postSingleUpdate device: camera id 0 status STATUS_PRESENT
22:23:46.588 ViewRootIm...nActivity]  I  performTraversals mFirst=true windowShouldResize=true viewVisibilityChanged=false mForceNextWindowRelayout=false params={(0,0)(fillxfill) sim={adjust=resize forwardNavigation} layoutInDisplayCutoutMode=shortEdges ty=BASE_APPLICATION wanim=0x1030309
  fl=81810100
  pfl=12020040
  bhv=DEFAULT
  fitSides= naviIconColor=0}
22:23:46.588 CameraManagerGlobal      I  postSingleUpdate device: camera id 1 status STATUS_PRESENT
22:23:46.588 VideoCapabilities        W  Unsupported mime image/vnd.android.heic
22:23:46.588 CameraManagerGlobal      I  postSingleUpdate device: camera id 2 status STATUS_PRESENT
22:23:46.589 CameraManagerGlobal      I  postSingleUpdate device: camera id 3 status STATUS_PRESENT
22:23:46.589 CameraManagerGlobal      I  Camera 1 facing CAMERA_FACING_FRONT state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
22:23:46.589 CameraManagerGlobal      I  Camera 2 facing CAMERA_FACING_BACK state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
22:23:46.589 CameraManagerGlobal      I  Camera 20 facing CAMERA_FACING_BACK state now CAMERA_STATE_CLOSED for client com.sec.android.app.camera API Level 2User Id 0
22:23:46.589 CameraManagerGlobal      I  Camera 21 facing CAMERA_FACING_BACK state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
22:23:46.590 CameraManagerGlobal      I  Camera 23 facing CAMERA_FACING_BACK state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
22:23:46.590 CameraManagerGlobal      I  Camera 3 facing CAMERA_FACING_FRONT state now CAMERA_STATE_CLOSED for client com.samsung.adaptivebrightnessgo API Level 2User Id 0
22:23:46.590 CameraManagerGlobal      I  Camera 4 facing CAMERA_FACING_FRONT state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
22:23:46.590 CameraManagerGlobal      I  Camera 40 facing CAMERA_FACING_BACK state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
22:23:46.590 CameraManagerGlobal      I  Camera 41 facing CAMERA_FACING_FRONT state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
22:23:46.590 CameraManagerGlobal      I  Camera 52 facing CAMERA_FACING_BACK state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
22:23:46.596 Capacitor                D  Handling local request: https://localhost/styles/style.css
22:23:46.596 ViewRootIm...nActivity]  I  updateBlastSurfaceIfNeeded mBlastBufferQueue=null isSameSurfaceControl=false
22:23:46.597 BufferQueueProducer      E  Unable to open libpenguin.so: dlopen failed: library "libpenguin.so" not found.
22:23:46.598 BLASTBufferQueue         I  new BLASTBufferQueue, mName= ViewRootImpl@3c7f832[MainActivity] mNativeObject= 0xb400007c3b287680 sc.mNativeObject= 0xb400007c9e6f4540 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:2909 android.view.ViewRootImpl.relayoutWindow:9847 android.view.ViewRootImpl.performTraversals:3884 android.view.ViewRootImpl.doTraversal:3116 android.view.ViewRootImpl$TraversalRunnable.run:10885 android.view.Choreographer$CallbackRecord.run:1301 android.view.Choreographer$CallbackRecord.run:1309 android.view.Choreographer.doCallbacks:923 android.view.Choreographer.doFrame:852 android.view.Choreographer$FrameDisplayEventReceiver.run:1283 
22:23:46.598 BLASTBufferQueue         I  update, w= 1080 h= 2400 mName = ViewRootImpl@3c7f832[MainActivity] mNativeObject= 0xb400007c3b287680 sc.mNativeObject= 0xb400007c9e6f4540 format= -1 caller= android.graphics.BLASTBufferQueue.<init>:84 android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:2909 android.view.ViewRootImpl.relayoutWindow:9847 android.view.ViewRootImpl.performTraversals:3884 android.view.ViewRootImpl.doTraversal:3116 android.view.ViewRootImpl$TraversalRunnable.run:10885 
22:23:46.598 Capacitor                D  Handling local request: https://localhost/styles/variables-gui.css
22:23:46.598 ViewRootIm...nActivity]  I  Relayout returned: old=(0,0,1080,2400) new=(0,0,1080,2400) req=(1080,2400)0 dur=8 res=0x3 s={true 0xb400007c9e5d1800} ch=true seqId=0
22:23:46.599 ViewRootIm...nActivity]  I  mThreadedRenderer.initialize() mSurface={isValid=true 0xb400007c9e5d1800} hwInitialized=true
22:23:46.600 ViewRootIm...nActivity]  I  reportNextDraw android.view.ViewRootImpl.performTraversals:4438 android.view.ViewRootImpl.doTraversal:3116 android.view.ViewRootImpl$TraversalRunnable.run:10885 android.view.Choreographer$CallbackRecord.run:1301 android.view.Choreographer$CallbackRecord.run:1309 
22:23:46.600 Capacitor                D  Handling local request: https://localhost/styles/common-screens.css
22:23:46.601 ViewRootIm...nActivity]  I  Setup new sync id=0
22:23:46.601 ViewRootIm...nActivity]  I  Setting syncFrameCallback
22:23:46.601 ViewRootIm...nActivity]  I  registerCallbacksForSync syncBuffer=false
22:23:46.602 OpenGLRenderer           D  eglCreateWindowSurface
22:23:46.605 AudioCapabilities        W  Unsupported mime audio/x-ape
22:23:46.606 AudioCapabilities        W  Unsupported mime audio/x-ima
22:23:46.606 AudioCapabilities        W  Unsupported mime audio/mpeg-L1
22:23:46.606 Capacitor                D  Handling local request: https://localhost/styles/pokedex.css
22:23:46.606 AudioCapabilities        W  Unsupported mime audio/mpeg-L2
22:23:46.606 VideoCapabilities        W  Unsupported mime video/mp43
22:23:46.606 VideoCapabilities        W  Unsupported mime video/wvc1
22:23:46.607 VideoCapabilities        W  Unsupported mime video/x-ms-wmv
22:23:46.607 AudioCapabilities        W  Unsupported mime audio/x-ms-wma
22:23:46.607 VideoCapabilities        W  Unsupported mime video/x-ms-wmv7
22:23:46.607 VideoCapabilities        W  Unsupported mime video/x-ms-wmv8
22:23:46.610 Capacitor                D  Handling local request: https://localhost/styles/encounters-screen.css
22:23:46.610 ViewRootIm...nActivity]  I  Received frameDrawingCallback syncResult=0 frameNum=1.
22:23:46.610 ViewRootIm...nActivity]  I  Setting up sync and frameCommitCallback
22:23:46.613 Capacitor                D  Handling local request: https://localhost/lib/turf.min.js
22:23:46.614 Capacitor                D  Handling local request: https://localhost/main.js
22:23:46.616 Capacitor                D  Handling local request: https://localhost/styles/fab-submenu.css
22:23:46.617 Capacitor                D  Handling local request: https://localhost/styles/pokemon-caught-screen.css
22:23:46.632 BLASTBufferQueue         I  [ViewRootImpl@3c7f832[MainActivity]#0](f:0,a:0) onFrameAvailable the first frame is available
22:23:46.633 ViewRootIm...nActivity]  I  Received frameCommittedCallback lastAttemptedDrawFrameNum=1 didProduceBuffer=true
22:23:46.634 OpenGLRenderer           D  CFMS:: SetUp Pid : 24859    Tid : 24888
22:23:46.635 Parcel                   W  Expecting binder but got null!
22:23:46.635 ViewRootIm...nActivity]  I  onSyncComplete
22:23:46.635 ViewRootIm...nActivity]  I  setupSync seqId=0 mSyncId=0 fn=1 caller=android.view.ViewRootImpl$$ExternalSyntheticLambda11.accept:6 android.window.SurfaceSyncer.lambda$setupSync$1$android-window-SurfaceSyncer:128 android.window.SurfaceSyncer$$ExternalSyntheticLambda1.accept:8 android.window.SurfaceSyncer$SyncSet.checkIfSyncIsComplete:382 android.window.SurfaceSyncer$SyncSet.markSyncReady:359 android.window.SurfaceSyncer.markSyncReady:151 android.view.ViewRootImpl.performTraversals:4503 
22:23:46.642 ViewRootIm...nActivity]  I  reportDrawFinished seqId=0 mSyncId=-1 fn=1 mSurfaceChangedTransaction=0xb400007c9e615500
22:23:46.661 Capacitor                D  Handling local request: https://localhost/styles/type-colors.css
22:23:46.661 Capacitor                D  Handling local request: https://localhost/fonts/poppins.css
22:23:46.662 Capacitor                D  Handling local request: https://localhost/styles/battle-screen.css
22:23:46.694 ViewRootIm...nActivity]  I  MSG_WINDOW_FOCUS_CHANGED 1 0
22:23:46.694 ViewRootIm...nActivity]  I  mThreadedRenderer.initializeIfNeeded()#2 mSurface={isValid=true 0xb400007c9e5d1800}
22:23:46.696 InputMethodManager       D  startInputInner - Id : 0
22:23:46.696 InputMethodManager       I  startInputInner - mService.startInputOrWindowGainedFocus
22:23:46.705 InputMethodManager       D  startInputInner - Id : 0
22:23:46.723 InsetsController         D  onStateChanged: InsetsState: {mDisplayFrame=Rect(0, 0 - 1080, 2400), mDisplayCutout=DisplayCutout{insets=Rect(0, 88 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 0 - 0, 0), Rect(512, 0 - 568, 88), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2400 physicalDisplayWidth=1080 physicalDisplayHeight=2400 density={3.0} cutoutSpec={M 0,0 M 0,10.71963616907435 a 9.280363830925644,9.280363830925644 0 1,0 0,18.56072766185129 a 9.280363830925644,9.280363830925644 0 1,0 0,-18.56072766185129 Z @dp} rotation={0} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=96, center=Point(96, 96)}, RoundedCorner{position=TopRight, radius=96, center=Point(984, 96)}, RoundedCorner{position=BottomRight, radius=96, center=Point(984, 2304)}, RoundedCorner{position=BottomLeft, radius=96, center=Point(96, 2304)}]}  mRoundedCornerFrame=Rect(0, 0 - 1080, 2400), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(948, 0 - 1080, 88) rotation=0}, mSources= { InsetsSource: {mType=ITYPE_STATUS_BAR, mFrame=[0,0][1080,88], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_NAVIGATION_BAR, mFrame=[0,2256][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_LEFT_GESTURES, mFrame=[0,0][0,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_RIGHT_GESTURES, mFrame=[1080,0][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_TOP_MANDATORY_GESTURES, mFrame=[0,0][1080,124], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_BOTTOM_MANDATORY_GESTURES, mFrame=[0,2256][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_LEFT_DISPLAY_CUTOUT, mFrame=[0,0][-100000,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_TOP_DISPLAY_CUTOUT, mFrame=[0,0][1080,88], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_RIGHT_DISPLAY_CUTOUT, mFrame=[100000,0][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_BOTTOM_DISPLAY_CUTOUT, mFrame=[0,100000][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_TOP_TAPPABLE_ELEMENT, mFrame=[0,0][1080,88], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_BOTTOM_TAPPABLE_ELEMENT, mFrame=[0,2256][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_IME, mFrame=[0,0][0,0], mVisibleFrame=[0,1692][1080,2400], mVisible=false, mInsetsRoundedCornerFrame=false} } host=com.example.gpspokemonapp/com.example.gpspokemonapp.MainActivity from=android.view.ViewRootImpl$ViewRootHandler.handleMessageImpl:6740
22:23:46.862 Capacitor                D  Handling local request: https://localhost/utils/logger.js
22:23:46.863 Capacitor                D  Handling local request: https://localhost/config.js
22:23:46.863 Capacitor                D  Handling local request: https://localhost/state/game-state.js
22:23:46.864 Capacitor                D  Handling local request: https://localhost/services/map-renderer.js
22:23:46.865 Capacitor                D  Handling local request: https://localhost/services/player-renderer.js
22:23:46.865 Capacitor                D  Handling local request: https://localhost/services/pokemon-spawner.js
22:23:46.865 Capacitor                D  Handling local request: https://localhost/ui/FabManager.js
22:23:46.865 Capacitor                D  Handling local request: https://localhost/storage/storage-service.js
22:23:46.866 Capacitor                D  Handling local request: https://localhost/storage/encountersStorage.js
22:23:46.866 Capacitor                D  Handling local request: https://localhost/capacitor/geolocation.js
22:23:46.866 Capacitor                D  Handling local request: https://localhost/capacitor/keep-awake.js
22:23:46.866 Capacitor                D  Handling local request: https://localhost/capacitor/app.js
22:23:46.867 Capacitor                D  Handling local request: https://localhost/services/pokemon-manager.js
22:23:46.867 Capacitor                D  Handling local request: https://localhost/capacitor/time-events.js
22:23:46.867 Capacitor                D  Handling local request: https://localhost/services/trainer-spawner.js
22:23:46.879 Capacitor                D  Handling local request: https://localhost/landuse-pokemon-types.js
22:23:46.879 Capacitor                D  Handling local request: https://localhost/Pokemon.js
22:23:46.880 Capacitor                D  Handling local request: https://localhost/utils/pokemon-display-names.js
22:23:46.880 Capacitor                D  Handling local request: https://localhost/pokemon-grid.js
22:23:46.881 Capacitor                D  Handling local request: https://localhost/overpass-landuse.js
22:23:46.882 Capacitor                D  Handling local request: https://localhost/services/spawn-levels.js
22:23:46.882 Capacitor                D  Handling local request: https://localhost/utils/pokemon-utils.js
22:23:46.883 Capacitor                D  Handling local request: https://localhost/ui/BattleScreen.js
22:23:46.883 Capacitor                D  Handling local request: https://localhost/ui/FabSubmenuManager.js
22:23:46.884 Capacitor                D  Handling local request: https://localhost/Trainer.js
22:23:46.886 Capacitor                D  Handling local request: https://localhost/lib/turf.js
22:23:46.888 Capacitor                D  Handling local request: https://localhost/services/experience-system.js
22:23:46.891 Capacitor                D  Handling local request: https://localhost/ui/Component.js
22:23:46.892 Capacitor                D  Handling local request: https://localhost/services/battle-calc.js
22:23:46.893 Capacitor                D  Handling local request: https://localhost/utils/battle-utils.js
22:23:46.893 Capacitor                D  Handling local request: https://localhost/ui/EncountersScreen.js
22:23:46.894 Capacitor                D  Handling local request: https://localhost/ui/PokedexScreen.js
22:23:46.894 Capacitor                D  Handling local request: https://localhost/ui/PokemonCaughtScreen.js
22:23:46.900 Capacitor                D  Handling local request: https://localhost/storage/teamStorage.js
22:23:46.903 Capacitor/Console        I  File: https://localhost/utils/logger.js - Line 24 - Msg: Logger initialized in Capacitor environment
22:23:46.903 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/storage/storage-service.js:9] StorageService initialized [object Object]
22:23:46.904 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/state/game-state.js:87] GameState initialized
22:23:46.905 Capacitor                D  Handling local request: https://localhost/pokemon-types-battle.json
22:23:46.907 Capacitor/Console        I  File: https://localhost/utils/logger.js - Line 135 - Msg: [https://localhost/main.js:40] Initializing app...
22:23:46.910 Capacitor/Console        I  File: https://localhost/utils/logger.js - Line 135 - Msg: [https://localhost/state/game-state.js:98] Loaded pokedex from storage - count: 307
22:23:46.912 Capacitor                D  Handling local request: https://localhost/pokedex-151.json
22:23:46.914 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/battle-calc.js:43] Type effectiveness data loaded successfully
22:23:46.915 Capacitor                D  Handling local request: https://localhost/favicon.ico
22:23:46.918 Capacitor/Console        I  File: https://localhost/utils/logger.js - Line 135 - Msg: [https://localhost/state/game-state.js:132] Pokedex data loaded - pokemonCount: 151, chainsCount: 78
22:23:46.918 Capacitor/Console        I  File: https://localhost/utils/logger.js - Line 135 - Msg: [https://localhost/state/game-state.js:103] GameState initialized successfully
22:23:46.920 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-manager.js:36] Loaded 195 Pokemon from storage
22:23:46.920 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Owei with dex_number: 102 (from evolutionData.dex: 102)
22:23:46.921 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 5 (common): 100
22:23:46.921 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Owei (Lvl 5) with 100 XP
22:23:46.921 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Owei (ID: ma4h8c95q1mw6h53q): 102
22:23:46.922 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Owei with 100 XP (Level 5) from _experience
22:23:46.922 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Pikachu with dex_number: 25 (from evolutionData.dex: 25)
22:23:46.922 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 5 (common): 100
22:23:46.923 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Pikachu (Lvl 5) with 100 XP
22:23:46.923 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Pikachu (ID: maedl8kncy8tkrc): 25
22:23:46.923 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Pikachu with 100 XP (Level 5) from _experience
22:23:46.923 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Rattikarl with dex_number: 20 (from evolutionData.dex: 20)
22:23:46.924 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 5 (common): 100
22:23:46.924 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Rattikarl (Lvl 5) with 100 XP
22:23:46.924 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Rattikarl (ID: ma8fh5klstpqlzue9): 20
22:23:46.924 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Rattikarl with 100 XP (Level 5) from _experience
22:23:46.924 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon psyduck with dex_number: 54 (from evolutionData.dex: 54)
22:23:46.924 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 12 (common): 1382
22:23:46.924 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized psyduck (Lvl 12) with 1382 XP
22:23:46.924 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Enton (ID: ma8f6qpn3l8sze7ab): 54
22:23:46.924 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Enton with 1540 XP (Level 12) from _experience
22:23:46.925 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Pikachu with dex_number: 25 (from evolutionData.dex: 25)
22:23:46.925 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 2 (common): 6
22:23:46.925 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Pikachu (Lvl 2) with 6 XP
22:23:46.925 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Pikachu (ID: maj8kbxuxq7q8vt): 25
22:23:46.925 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Pikachu with 6 XP (Level 2) from _experience
22:23:46.925 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:46.925 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 5 (common): 100
22:23:46.925 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized zubat (Lvl 5) with 100 XP
22:23:46.926 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: mak55akldyt3giz): 41
22:23:46.926 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 100 XP (Level 5) from _experience
22:23:46.926 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon bulbasaur with dex_number: 1 (from evolutionData.dex: 1)
22:23:46.926 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 9 (common): 583
22:23:46.926 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized bulbasaur (Lvl 9) with 583 XP
22:23:46.926 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Bisasam (ID: mak315u9nx4auxv): 1
22:23:46.926 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Bisasam with 694 XP (Level 9) from _experience
22:23:46.926 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Voltobal with dex_number: 100 (from evolutionData.dex: 100)
22:23:46.927 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 5 (common): 100
22:23:46.927 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Voltobal (Lvl 5) with 100 XP
22:23:46.927 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Voltobal (ID: ma6y45cl943ih1zte): 100
22:23:46.927 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Voltobal with 150 XP (Level 5) from _experience
22:23:46.928 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Enton with dex_number: 54 (from evolutionData.dex: 54)
22:23:46.928 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 5 (common): 100
22:23:46.928 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Enton (Lvl 5) with 100 XP
22:23:46.928 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Enton (ID: ma8fh59iggef9bbt5): 54
22:23:46.928 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Enton with 100 XP (Level 5) from _experience
22:23:46.928 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Dodri with dex_number: 85 (from evolutionData.dex: 85)
22:23:46.928 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 5 (common): 100
22:23:46.928 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Dodri (Lvl 5) with 100 XP
22:23:46.928 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Dodri (ID: ma8fh5klkne602ey9): 85
22:23:46.929 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Dodri with 100 XP (Level 5) from _experience
22:23:46.929 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:46.929 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: mamy0lj0bpcc5rg): 41
22:23:46.929 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 0 XP (Level 1) from _experience
22:23:46.929 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Taubsi with dex_number: 16 (from evolutionData.dex: 16)
22:23:46.929 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 5 (common): 100
22:23:46.929 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Taubsi (Lvl 5) with 100 XP
22:23:46.929 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Taubsi (ID: ma8fusfqg2lhhkvyd): 16
22:23:46.930 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Taubsi with 100 XP (Level 5) from _experience
22:23:46.930 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:46.930 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 4 (common): 51
22:23:46.930 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Zubat (Lvl 4) with 51 XP
22:23:46.930 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: maodebvckklw2z7): 41
22:23:46.930 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 51 XP (Level 4) from _experience
22:23:46.930 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon caterpie with dex_number: 12 (from evolutionData.dex: 12)
22:23:46.930 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 14 (common): 2195
22:23:46.930 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized caterpie (Lvl 14) with 2195 XP
22:23:46.931 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Smettbo (ID: mao322703yi9bii): 12
22:23:46.931 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Smettbo with 2314 XP (Level 14) from _experience
22:23:46.932 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Smogon with dex_number: 109 (from evolutionData.dex: 109)
22:23:46.932 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 5 (common): 100
22:23:46.932 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Smogon (Lvl 5) with 100 XP
22:23:46.932 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Smogon (ID: mao3y6w2zivxrvm): 109
22:23:46.932 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Smogon with 100 XP (Level 5) from _experience
22:23:46.932 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:46.932 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 8 (common): 409
22:23:46.932 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Zubat (Lvl 8) with 409 XP
22:23:46.932 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: man02p3qqhwuvtv): 41
22:23:46.933 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 409 XP (Level 8) from _experience
22:23:46.933 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon pidgey with dex_number: 16 (from evolutionData.dex: 16)
22:23:46.933 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 11 (common): 1064
22:23:46.933 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized pidgey (Lvl 11) with 1064 XP
22:23:46.933 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Taubsi (ID: maktxhr6v1va7d2): 16
22:23:46.933 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Taubsi with 1280 XP (Level 11) from _experience
22:23:46.933 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon abra with dex_number: 63 (from evolutionData.dex: 63)
22:23:46.933 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 11 (common): 1064
22:23:46.933 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized abra (Lvl 11) with 1064 XP
22:23:46.934 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Abra (ID: majoicec3rhg22a): 63
22:23:46.934 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Abra with 1250 XP (Level 11) from _experience
22:23:46.934 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Bisasam with dex_number: 1 (from evolutionData.dex: 1)
22:23:46.934 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 3 (starter): 27
22:23:46.934 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Bisasam (Lvl 3) with 27 XP
22:23:46.934 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Bisasam (ID: maqxer32qaknyxm): 1
22:23:46.934 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Bisasam with 27 XP (Level 3) from _experience
22:23:46.934 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Pinsir with dex_number: 127 (from evolutionData.dex: 127)
22:23:46.934 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 11 (scarce): 1064
22:23:46.935 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Pinsir (Lvl 11) with 1064 XP
22:23:46.935 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Pinsir (ID: mao3226uxvce5g2): 127
22:23:46.935 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Pinsir with 1064 XP (Level 11) from _experience
22:23:46.935 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Smettbo with dex_number: 12 (from evolutionData.dex: 12)
22:23:46.935 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 10 (common): 800
22:23:46.935 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Smettbo (Lvl 10) with 800 XP
22:23:46.935 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Smettbo (ID: mao2nib80731sv9): 12
22:23:46.935 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Smettbo with 800 XP (Level 10) from _experience
22:23:46.935 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Schlurp with dex_number: 108 (from evolutionData.dex: 108)
22:23:46.936 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 9 (common): 583
22:23:46.936 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Schlurp (Lvl 9) with 583 XP
22:23:46.936 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Schlurp (ID: majn2l9sk3u46ty): 108
22:23:46.936 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Schlurp with 583 XP (Level 9) from _experience
22:23:46.937 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Abra with dex_number: 63 (from evolutionData.dex: 63)
22:23:46.937 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 10 (common): 800
22:23:46.937 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Abra (Lvl 10) with 800 XP
22:23:46.937 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Abra (ID: majktqd1qzqxqr7): 63
22:23:46.937 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Abra with 800 XP (Level 10) from _experience
22:23:46.937 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Pikachu with dex_number: 25 (from evolutionData.dex: 25)
22:23:46.938 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 8 (common): 409
22:23:46.938 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Pikachu (Lvl 8) with 409 XP
22:23:46.938 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Pikachu (ID: majetuospbx2au4): 25
22:23:46.938 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Pikachu with 409 XP (Level 8) from _experience
22:23:46.938 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Owei with dex_number: 102 (from evolutionData.dex: 102)
22:23:46.938 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 5 (common): 100
22:23:46.938 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Owei (Lvl 5) with 100 XP
22:23:46.938 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Owei (ID: mad0ss3zfwiswzipv): 102
22:23:46.939 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Owei with 100 XP (Level 5) from _experience
22:23:46.939 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Porygon with dex_number: 137 (from evolutionData.dex: 137)
22:23:46.939 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 5 (rare): 350
22:23:46.939 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Porygon (Lvl 5) with 350 XP
22:23:46.939 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Porygon (ID: ma79yxvx0qdpm68ce): 137
22:23:46.939 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Porygon with 350 XP (Level 5) from _experience
22:23:46.939 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:46.939 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 14 (common): 2195
22:23:46.940 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Zubat (Lvl 14) with 2195 XP
22:23:46.940 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: martmmto6yf4j0f): 41
22:23:46.940 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 2195 XP (Level 14) from _experience
22:23:46.940 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon spearow with dex_number: 21 (from evolutionData.dex: 21)
22:23:46.940 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 29 (common): 19511
22:23:46.940 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized spearow (Lvl 29) with 19511 XP
22:23:46.940 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Habitak (ID: mao4pusjexkuzza): 21
22:23:46.940 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Habitak with 19974 XP (Level 29) from _experience
22:23:46.941 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Rattfratz with dex_number: 19 (from evolutionData.dex: 19)
22:23:46.941 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 5 (common): 100
22:23:46.941 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Rattfratz (Lvl 5) with 100 XP
22:23:46.941 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Rattfratz (ID: ma6wmoz7xnbzpxctf): 19
22:23:46.941 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Rattfratz with 100 XP (Level 5) from _experience
22:23:46.941 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Myrapla with dex_number: 43 (from evolutionData.dex: 43)
22:23:46.942 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 5 (common): 100
22:23:46.942 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Myrapla (Lvl 5) with 100 XP
22:23:46.942 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Myrapla (ID: ma6vs9t7unm5sx7nj): 43
22:23:46.942 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Myrapla with 100 XP (Level 5) from _experience
22:23:46.942 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Krabby with dex_number: 98 (from evolutionData.dex: 98)
22:23:46.942 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 5 (common): 100
22:23:46.942 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Krabby (Lvl 5) with 100 XP
22:23:46.942 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Krabby (ID: ma6wmoz748c0we3ep): 98
22:23:46.943 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Krabby with 100 XP (Level 5) from _experience
22:23:46.943 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon pidgey with dex_number: 16 (from evolutionData.dex: 16)
22:23:46.943 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 5 (common): 100
22:23:46.943 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized pidgey (Lvl 5) with 100 XP
22:23:46.943 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Taubsi (ID: ma6x1jweapwaugqm4): 16
22:23:46.943 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Taubsi with 130 XP (Level 5) from _experience
22:23:46.944 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon rattata with dex_number: 19 (from evolutionData.dex: 19)
22:23:46.944 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 9 (common): 583
22:23:46.944 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized rattata (Lvl 9) with 583 XP
22:23:46.944 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Rattfratz (ID: ma6x1jwevh00xji40): 19
22:23:46.944 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Rattfratz with 760 XP (Level 9) from _experience
22:23:46.944 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon jynx with dex_number: 124 (from evolutionData.dex: 124)
22:23:46.944 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 13 (common): 1757
22:23:46.944 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized jynx (Lvl 13) with 1757 XP
22:23:46.945 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Rossana (ID: ma6xml4iqbdk0m0g0): 124
22:23:46.945 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Rossana with 1840 XP (Level 13) from _experience
22:23:46.945 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon pikachu with dex_number: 25 (from evolutionData.dex: 25)
22:23:46.945 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 16 (starter): 4096
22:23:46.945 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized pikachu (Lvl 16) with 4096 XP
22:23:46.945 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Pikachu (ID: starter-pikachu): 25
22:23:46.946 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Pikachu with 4513 XP (Level 16) from _experience
22:23:46.946 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Pikachu with dex_number: 25 (from evolutionData.dex: 25)
22:23:46.946 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 15 (common): 2700
22:23:46.946 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Pikachu (Lvl 15) with 2700 XP
22:23:46.946 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Pikachu (ID: maj8kbxvol3ccpk): 25
22:23:46.946 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Pikachu with 2700 XP (Level 15) from _experience
22:23:46.946 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Pikachu with dex_number: 25 (from evolutionData.dex: 25)
22:23:46.947 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 9 (common): 583
22:23:46.947 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Pikachu (Lvl 9) with 583 XP
22:23:46.947 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Pikachu (ID: maf28s8158knn8k): 25
22:23:46.947 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Pikachu with 583 XP (Level 9) from _experience
22:23:46.947 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Amonitas with dex_number: 138 (from evolutionData.dex: 138)
22:23:46.947 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 5 (rare): 350
22:23:46.947 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Amonitas (Lvl 5) with 350 XP
22:23:46.948 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Amonitas (ID: ma6y45clj1hbo5ibp): 138
22:23:46.948 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Amonitas with 350 XP (Level 5) from _experience
22:23:46.948 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Krabby with dex_number: 98 (from evolutionData.dex: 98)
22:23:46.948 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 5 (common): 100
22:23:46.948 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Krabby (Lvl 5) with 100 XP
22:23:46.948 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Krabby (ID: ma6yddcse81fvwzhs): 98
22:23:46.948 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Krabby with 100 XP (Level 5) from _experience
22:23:46.948 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Pantimos with dex_number: 122 (from evolutionData.dex: 122)
22:23:46.949 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 13 (rare): 2116
22:23:46.949 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Pantimos (Lvl 13) with 2116 XP
22:23:46.949 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Pantimos (ID: marzz5niwkoa1gr): 122
22:23:46.949 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Pantimos with 2116 XP (Level 13) from _experience
22:23:46.949 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon zubat with dex_number: 42 (from evolutionData.dex: 42)
22:23:46.949 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 38 (common): 43897
22:23:46.949 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized zubat (Lvl 38) with 43897 XP
22:23:46.950 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Golbat (ID: mat9bjri86tknj0): 42
22:23:46.950 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Golbat with 43897 XP (Level 38) from _experience
22:23:46.950 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:46.950 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 19 (common): 5487
22:23:46.950 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Zubat (Lvl 19) with 5487 XP
22:23:46.950 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: matb6ad6iw3d2sr): 41
22:23:46.950 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 5487 XP (Level 19) from _experience
22:23:46.950 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:46.951 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 16 (common): 3276
22:23:46.951 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Zubat (Lvl 16) with 3276 XP
22:23:46.951 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: matb6acmh0khuhv): 41
22:23:46.951 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 3276 XP (Level 16) from _experience
22:23:46.951 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon zubat with dex_number: 42 (from evolutionData.dex: 42)
22:23:46.952 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 46 (common): 77868
22:23:46.952 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized zubat (Lvl 46) with 77868 XP
22:23:46.952 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Golbat (ID: matb6acvrhkqq40): 42
22:23:46.952 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Golbat with 77868 XP (Level 46) from _experience
22:23:46.952 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon zubat with dex_number: 42 (from evolutionData.dex: 42)
22:23:46.952 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 26 (common): 14060
22:23:46.952 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized zubat (Lvl 26) with 14060 XP
22:23:46.952 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Golbat (ID: matk0gztm3pbix1): 42
22:23:46.952 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Golbat with 14060 XP (Level 26) from _experience
22:23:46.953 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon zubat with dex_number: 42 (from evolutionData.dex: 42)
22:23:46.953 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 45 (common): 72900
22:23:46.953 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized zubat (Lvl 45) with 72900 XP
22:23:46.953 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Golbat (ID: matttm0z4jp086r): 42
22:23:46.953 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Golbat with 72900 XP (Level 45) from _experience
22:23:46.953 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:46.954 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 14 (common): 2195
22:23:46.954 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Zubat (Lvl 14) with 2195 XP
22:23:46.954 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: matttm0p56a6klg): 41
22:23:46.954 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 2195 XP (Level 14) from _experience
22:23:46.954 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon zubat with dex_number: 42 (from evolutionData.dex: 42)
22:23:46.954 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 33 (common): 28749
22:23:46.954 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized zubat (Lvl 33) with 28749 XP
22:23:46.954 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Golbat (ID: matuzcizm5ul2zx): 42
22:23:46.955 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Golbat with 28749 XP (Level 33) from _experience
22:23:46.955 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon zubat with dex_number: 42 (from evolutionData.dex: 42)
22:23:46.955 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 27 (common): 15746
22:23:46.955 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized zubat (Lvl 27) with 15746 XP
22:23:46.955 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Golbat (ID: matuzciqk43y1ux): 42
22:23:46.955 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Golbat with 15746 XP (Level 27) from _experience
22:23:46.956 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Golbat with dex_number: 42 (from evolutionData.dex: 42)
22:23:46.956 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 26 (common): 14060
22:23:46.956 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Golbat (Lvl 26) with 14060 XP
22:23:46.956 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Golbat (ID: mau11mfdn0vh5op): 42
22:23:46.956 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Golbat with 14128 XP (Level 26) from _experience
22:23:46.956 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:46.956 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 7 (common): 274
22:23:46.957 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Zubat (Lvl 7) with 274 XP
22:23:46.957 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: mau11mfnyp2zzqx): 41
22:23:46.957 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 274 XP (Level 7) from _experience
22:23:46.957 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon zubat with dex_number: 42 (from evolutionData.dex: 42)
22:23:46.957 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 42 (common): 59270
22:23:46.957 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized zubat (Lvl 42) with 59270 XP
22:23:46.957 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Golbat (ID: mau11mfr5gq8jka): 42
22:23:46.957 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Golbat with 59270 XP (Level 42) from _experience
22:23:46.958 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Rossana with dex_number: 124 (from evolutionData.dex: 124)
22:23:46.958 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 5 (common): 100
22:23:46.958 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Rossana (Lvl 5) with 100 XP
22:23:46.958 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Rossana (ID: ma6x1jwesvxfq4273): 124
22:23:46.958 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Rossana with 100 XP (Level 5) from _experience
22:23:46.958 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Amonitas with dex_number: 138 (from evolutionData.dex: 138)
22:23:46.958 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 5 (rare): 350
22:23:46.959 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Amonitas (Lvl 5) with 350 XP
22:23:46.959 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Amonitas (ID: ma6y45clxn0a810ag): 138
22:23:46.959 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Amonitas with 350 XP (Level 5) from _experience
22:23:46.959 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:46.959 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 4 (common): 51
22:23:46.959 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Zubat (Lvl 4) with 51 XP
22:23:46.959 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: mau30lyz2pbcuqd): 41
22:23:46.959 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 51 XP (Level 4) from _experience
22:23:46.959 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Voltobal with dex_number: 100 (from evolutionData.dex: 100)
22:23:46.960 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 5 (common): 100
22:23:46.960 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Voltobal (Lvl 5) with 100 XP
22:23:46.960 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Voltobal (ID: ma6wmoz7cbpqscq2a): 100
22:23:46.960 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Voltobal with 100 XP (Level 5) from _experience
22:23:46.960 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Rossana with dex_number: 124 (from evolutionData.dex: 124)
22:23:46.960 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 5 (common): 100
22:23:46.960 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Rossana (Lvl 5) with 100 XP
22:23:46.960 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Rossana (ID: ma6xml4i32l1xh9mo): 124
22:23:46.960 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Rossana with 100 XP (Level 5) from _experience
22:23:46.961 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Ponita with dex_number: 77 (from evolutionData.dex: 77)
22:23:46.961 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 6 (common): 172
22:23:46.961 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Ponita (Lvl 6) with 172 XP
22:23:46.961 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Ponita (ID: madvq5u3c7c7gn1): 77
22:23:46.961 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Ponita with 172 XP (Level 6) from _experience
22:23:46.961 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Kokowei with dex_number: 103 (from evolutionData.dex: 103)
22:23:46.961 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 5 (common): 100
22:23:46.961 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Kokowei (Lvl 5) with 100 XP
22:23:46.962 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Kokowei (ID: madvhb4vk0grdue): 103
22:23:46.962 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Kokowei with 100 XP (Level 5) from _experience
22:23:46.962 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Rossana with dex_number: 124 (from evolutionData.dex: 124)
22:23:46.962 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 5 (common): 100
22:23:46.962 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Rossana (Lvl 5) with 100 XP
22:23:46.962 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Rossana (ID: ma8f6qpmtk3y6w9zt): 124
22:23:46.962 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Rossana with 100 XP (Level 5) from _experience
22:23:46.962 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Kangama with dex_number: 115 (from evolutionData.dex: 115)
22:23:46.963 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 5 (scarce): 100
22:23:46.963 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Kangama (Lvl 5) with 100 XP
22:23:46.963 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Kangama (ID: mas3jkd8ox58bu9): 115
22:23:46.963 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Kangama with 100 XP (Level 5) from _experience
22:23:46.963 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon paras with dex_number: 46 (from evolutionData.dex: 46)
22:23:46.963 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 5 (common): 100
22:23:46.963 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized paras (Lvl 5) with 100 XP
22:23:46.964 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Paras (ID: maawxp7xj7wv94d0i): 46
22:23:46.964 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Paras with 100 XP (Level 5) from _experience
22:23:46.964 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon zubat with dex_number: 42 (from evolutionData.dex: 42)
22:23:46.964 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 33 (common): 28749
22:23:46.964 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized zubat (Lvl 33) with 28749 XP
22:23:46.964 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Golbat (ID: maumfzdrr39wa32): 42
22:23:46.964 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Golbat with 28749 XP (Level 33) from _experience
22:23:46.964 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon zubat with dex_number: 42 (from evolutionData.dex: 42)
22:23:46.964 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 29 (common): 19511
22:23:46.964 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized zubat (Lvl 29) with 19511 XP
22:23:46.965 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Golbat (ID: maupwwer1uu5lyi): 42
22:23:46.965 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Golbat with 19511 XP (Level 29) from _experience
22:23:46.965 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon zubat with dex_number: 42 (from evolutionData.dex: 42)
22:23:46.965 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 30 (common): 21600
22:23:46.965 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized zubat (Lvl 30) with 21600 XP
22:23:46.965 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Golbat (ID: maupwwewmb3gj41): 42
22:23:46.965 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Golbat with 21600 XP (Level 30) from _experience
22:23:46.965 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon zubat with dex_number: 42 (from evolutionData.dex: 42)
22:23:46.965 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 50 (common): 100000
22:23:46.966 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized zubat (Lvl 50) with 100000 XP
22:23:46.966 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Golbat (ID: maupwweuc9au0dg): 42
22:23:46.966 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Golbat with 100000 XP (Level 50) from _experience
22:23:46.966 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Nidoran with dex_number: 29 (from evolutionData.dex: 29)
22:23:46.966 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 5 (common): 100
22:23:46.966 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Nidoran (Lvl 5) with 100 XP
22:23:46.966 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Nidoran (ID: ma9n04o0f7jf5z7ks): 29
22:23:46.966 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Nidoran with 100 XP (Level 5) from _experience
22:23:46.966 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:46.967 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 20 (common): 6400
22:23:46.967 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Zubat (Lvl 20) with 6400 XP
22:23:46.967 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: martfg73zn7lf5k): 41
22:23:46.967 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 6400 XP (Level 20) from _experience
22:23:46.967 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:46.967 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 11 (common): 1064
22:23:46.967 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Zubat (Lvl 11) with 1064 XP
22:23:46.967 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: maviqyrnkl6ador): 41
22:23:46.967 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 1064 XP (Level 11) from _experience
22:23:46.967 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Ibitak with dex_number: 22 (from evolutionData.dex: 22)
22:23:46.968 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 5 (common): 100
22:23:46.968 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Ibitak (Lvl 5) with 100 XP
22:23:46.968 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Ibitak (ID: ma8fh5klnexdr4ahu): 22
22:23:46.968 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Ibitak with 100 XP (Level 5) from _experience
22:23:46.968 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Tragosso with dex_number: 104 (from evolutionData.dex: 104)
22:23:46.968 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 5 (common): 100
22:23:46.968 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Tragosso (Lvl 5) with 100 XP
22:23:46.968 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Tragosso (ID: ma58hq5m6n4g8wo5p): 104
22:23:46.969 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Tragosso with 100 XP (Level 5) from _experience
22:23:46.969 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:46.969 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 11 (common): 1064
22:23:46.969 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Zubat (Lvl 11) with 1064 XP
22:23:46.969 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: mavkuml70969ciy): 41
22:23:46.969 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 1064 XP (Level 11) from _experience
22:23:46.969 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Snobilikat with dex_number: 53 (from evolutionData.dex: 53)
22:23:46.969 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 5 (common): 100
22:23:46.969 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Snobilikat (Lvl 5) with 100 XP
22:23:46.970 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Snobilikat (ID: ma8fh5kl9rpaccw93): 53
22:23:46.970 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Snobilikat with 100 XP (Level 5) from _experience
22:23:46.970 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:46.970 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 19 (common): 5487
22:23:46.970 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Zubat (Lvl 19) with 5487 XP
22:23:46.970 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: maw0yk7c58r2e0g): 41
22:23:46.970 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 5487 XP (Level 19) from _experience
22:23:46.970 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:46.970 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 13 (common): 1757
22:23:46.970 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Zubat (Lvl 13) with 1757 XP
22:23:46.971 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: maw0yk6gscknmeh): 41
22:23:46.971 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 1757 XP (Level 13) from _experience
22:23:46.971 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:46.971 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 8 (common): 409
22:23:46.971 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Zubat (Lvl 8) with 409 XP
22:23:46.971 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: maw0yk6kno45qwl): 41
22:23:46.971 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 409 XP (Level 8) from _experience
22:23:46.971 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:46.972 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 15 (common): 2700
22:23:46.972 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Zubat (Lvl 15) with 2700 XP
22:23:46.972 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: maw3pynya1un5u3): 41
22:23:46.972 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 2700 XP (Level 15) from _experience
22:23:46.972 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:46.972 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 19 (common): 5487
22:23:46.972 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Zubat (Lvl 19) with 5487 XP
22:23:46.972 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: maw83bqtqq5j1hm): 41
22:23:46.972 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 5487 XP (Level 19) from _experience
22:23:46.973 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon zubat with dex_number: 42 (from evolutionData.dex: 42)
22:23:46.973 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 27 (common): 15746
22:23:46.973 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized zubat (Lvl 27) with 15746 XP
22:23:46.973 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Golbat (ID: maw83bqn9p3966r): 42
22:23:46.973 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Golbat with 15746 XP (Level 27) from _experience
22:23:46.973 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Tragosso with dex_number: 104 (from evolutionData.dex: 104)
22:23:46.973 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 22 (common): 8518
22:23:46.973 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Tragosso (Lvl 22) with 8518 XP
22:23:46.973 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Tragosso (ID: marzz5n9hxc24j0): 104
22:23:46.974 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Tragosso with 8518 XP (Level 22) from _experience
22:23:46.974 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon scyther with dex_number: 123 (from evolutionData.dex: 123)
22:23:46.974 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 31 (scarce): 23832
22:23:46.974 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized scyther (Lvl 31) with 23832 XP
22:23:46.974 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Sichlor (ID: mao3226w64s5k86): 123
22:23:46.974 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Sichlor with 25823 XP (Level 31) from _experience
22:23:46.974 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:46.974 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 12 (common): 1382
22:23:46.974 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Zubat (Lvl 12) with 1382 XP
22:23:46.975 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: mawe2m10r49t2tr): 41
22:23:46.975 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 1382 XP (Level 12) from _experience
22:23:46.975 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:46.975 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 11 (common): 1064
22:23:46.975 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Zubat (Lvl 11) with 1064 XP
22:23:46.975 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: mawem9t62783ft9): 41
22:23:46.975 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 1064 XP (Level 11) from _experience
22:23:46.975 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:46.975 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 12 (common): 1382
22:23:46.976 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Zubat (Lvl 12) with 1382 XP
22:23:46.976 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: mawemcpquiph2d2): 41
22:23:46.976 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 1382 XP (Level 12) from _experience
22:23:46.976 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon bellsprout with dex_number: 69 (from evolutionData.dex: 69)
22:23:46.976 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 17 (common): 3930
22:23:46.976 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized bellsprout (Lvl 17) with 3930 XP
22:23:46.976 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Knofensa (ID: mao0uv9sfcwl53v): 69
22:23:46.976 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Knofensa with 4380 XP (Level 17) from _experience
22:23:46.977 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon exeggcute with dex_number: 102 (from evolutionData.dex: 102)
22:23:46.977 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 31 (common): 23832
22:23:46.977 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized exeggcute (Lvl 31) with 23832 XP
22:23:46.977 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Owei (ID: madwlx1cpw837ub): 102
22:23:46.977 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Owei with 25831 XP (Level 31) from _experience
22:23:46.977 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon golbat with dex_number: 42 (from evolutionData.dex: 42)
22:23:46.977 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 28 (common): 17561
22:23:46.977 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized golbat (Lvl 28) with 17561 XP
22:23:46.978 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Golbat (ID: mawhir68qipi3r9): 42
22:23:46.978 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Golbat with 17561 XP (Level 28) from _experience
22:23:46.978 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:46.978 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 15 (common): 2700
22:23:46.978 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Zubat (Lvl 15) with 2700 XP
22:23:46.978 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: mawiwf5tqvclb3q): 41
22:23:46.978 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 2700 XP (Level 15) from _experience
22:23:46.978 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Kicklee with dex_number: 106 (from evolutionData.dex: 106)
22:23:46.978 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 20 (common): 6400
22:23:46.979 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Kicklee (Lvl 20) with 6400 XP
22:23:46.979 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Kicklee (ID: mawm16kiumjvo6v): 106
22:23:46.979 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Kicklee with 6400 XP (Level 20) from _experience
22:23:46.979 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Machollo with dex_number: 66 (from evolutionData.dex: 66)
22:23:46.979 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 15 (common): 2700
22:23:46.979 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Machollo (Lvl 15) with 2700 XP
22:23:46.979 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Machollo (ID: mawm16jzpeke4b6): 66
22:23:46.979 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Machollo with 2700 XP (Level 15) from _experience
22:23:46.980 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon jynx with dex_number: 124 (from evolutionData.dex: 124)
22:23:46.980 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 17 (common): 3930
22:23:46.980 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized jynx (Lvl 17) with 3930 XP
22:23:46.980 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Rossana (ID: mawm1byr5z6gmo5): 124
22:23:46.980 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Rossana with 3930 XP (Level 17) from _experience
22:23:46.980 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Evoli with dex_number: 133 (from evolutionData.dex: 133)
22:23:46.980 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 17 (scarce): 3930
22:23:46.980 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Evoli (Lvl 17) with 3930 XP
22:23:46.981 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Evoli (ID: mawmz5ffacs296s): 133
22:23:46.981 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Evoli with 3930 XP (Level 17) from _experience
22:23:46.981 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon mr-mime with dex_number: 122 (from evolutionData.dex: 122)
22:23:46.981 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 24 (rare): 12988
22:23:46.981 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized mr-mime (Lvl 24) with 12988 XP
22:23:46.981 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Pantimos (ID: mawmzdcbi51gf44): 122
22:23:46.981 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Pantimos with 12988 XP (Level 24) from _experience
22:23:46.981 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Pantimos with dex_number: 122 (from evolutionData.dex: 122)
22:23:46.981 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Pantimos (ID: mawmza7ed8ityt2): 122
22:23:46.982 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Pantimos with 81 XP (Level 1) from _experience
22:23:46.982 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:46.982 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 18 (common): 4665
22:23:46.982 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Zubat (Lvl 18) with 4665 XP
22:23:46.982 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: mawoqq50ocrftdr): 41
22:23:46.982 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 4665 XP (Level 18) from _experience
22:23:46.982 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:46.982 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 18 (common): 4665
22:23:46.982 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Zubat (Lvl 18) with 4665 XP
22:23:46.983 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: mawoqt3gxhh9rwc): 41
22:23:46.983 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 4665 XP (Level 18) from _experience
22:23:46.983 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:46.983 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 17 (common): 3930
22:23:46.983 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Zubat (Lvl 17) with 3930 XP
22:23:46.983 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: mawoqpgnjx1z9sc): 41
22:23:46.983 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 3930 XP (Level 17) from _experience
22:23:46.983 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:46.983 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 18 (common): 4665
22:23:46.984 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Zubat (Lvl 18) with 4665 XP
22:23:46.984 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: mawoqgeabkfknwn): 41
22:23:46.984 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 4665 XP (Level 18) from _experience
22:23:46.984 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon golbat with dex_number: 42 (from evolutionData.dex: 42)
22:23:46.984 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 27 (common): 15746
22:23:46.984 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized golbat (Lvl 27) with 15746 XP
22:23:46.984 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Golbat (ID: mawq9hu1fqbu6tx): 42
22:23:46.984 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Golbat with 15746 XP (Level 27) from _experience
22:23:46.984 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:46.985 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 19 (common): 5487
22:23:46.985 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Zubat (Lvl 19) with 5487 XP
22:23:46.985 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: mawq9o2xwvsrmc7): 41
22:23:46.985 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 5487 XP (Level 19) from _experience
22:23:46.985 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:46.985 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 17 (common): 3930
22:23:46.985 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Zubat (Lvl 17) with 3930 XP
22:23:46.985 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: mawrhpacww1x5ed): 41
22:23:46.985 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 3930 XP (Level 17) from _experience
22:23:46.986 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:46.986 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 17 (common): 3930
22:23:46.986 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Zubat (Lvl 17) with 3930 XP
22:23:46.986 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: mawtl0bvtm0s7ci): 41
22:23:46.986 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 3930 XP (Level 17) from _experience
22:23:46.986 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:46.986 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 12 (common): 1382
22:23:46.986 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Zubat (Lvl 12) with 1382 XP
22:23:46.986 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: maww3eqhgvvy2wh): 41
22:23:46.987 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 1382 XP (Level 12) from _experience
22:23:46.987 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon golbat with dex_number: 42 (from evolutionData.dex: 42)
22:23:46.987 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 22 (common): 8518
22:23:46.987 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized golbat (Lvl 22) with 8518 XP
22:23:46.987 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Golbat (ID: mawyjy7uxncvskh): 42
22:23:46.987 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Golbat with 8518 XP (Level 22) from _experience
22:23:46.987 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:46.988 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: mawyjydj98r6v3o): 41
22:23:46.988 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 0 XP (Level 1) from _experience
22:23:46.988 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:46.988 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 16 (common): 3276
22:23:46.988 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Zubat (Lvl 16) with 3276 XP
22:23:46.988 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: mawyk36sh57tqsa): 41
22:23:46.988 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 3276 XP (Level 16) from _experience
22:23:46.988 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:46.988 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 18 (common): 4665
22:23:46.989 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized zubat (Lvl 18) with 4665 XP
22:23:46.989 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: maxhamh2694c2fq): 41
22:23:46.989 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 4665 XP (Level 18) from _experience
22:23:46.989 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:46.989 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 21 (common): 7408
22:23:46.989 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized zubat (Lvl 21) with 7408 XP
22:23:46.989 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: maxhakahagom30s): 41
22:23:46.989 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 7408 XP (Level 21) from _experience
22:23:46.989 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Bibor with dex_number: 15 (from evolutionData.dex: 15)
22:23:46.989 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 18 (common): 4665
22:23:46.990 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Bibor (Lvl 18) with 4665 XP
22:23:46.990 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Bibor (ID: maxugu6ttk022iu): 15
22:23:46.990 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Bibor with 4665 XP (Level 18) from _experience
22:23:46.990 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon beedrill with dex_number: 15 (from evolutionData.dex: 15)
22:23:46.990 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 29 (common): 19511
22:23:46.990 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized beedrill (Lvl 29) with 19511 XP
22:23:46.990 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Bibor (ID: maxugsahmfxki0y): 15
22:23:46.990 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Bibor with 19511 XP (Level 29) from _experience
22:23:46.991 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Bibor with dex_number: 15 (from evolutionData.dex: 15)
22:23:46.991 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 18 (common): 4665
22:23:46.991 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Bibor (Lvl 18) with 4665 XP
22:23:46.991 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Bibor (ID: maxxa7ywckaxmvu): 15
22:23:46.991 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Bibor with 4665 XP (Level 18) from _experience
22:23:46.991 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon beedrill with dex_number: 15 (from evolutionData.dex: 15)
22:23:46.991 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 27 (common): 15746
22:23:46.991 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized beedrill (Lvl 27) with 15746 XP
22:23:46.991 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Bibor (ID: maxxa85rj955tnx): 15
22:23:46.992 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Bibor with 15746 XP (Level 27) from _experience
22:23:46.992 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:46.992 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 19 (common): 5487
22:23:46.992 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized zubat (Lvl 19) with 5487 XP
22:23:46.992 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: may7uhjamwybhk8): 41
22:23:46.992 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 5487 XP (Level 19) from _experience
22:23:46.992 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon golbat with dex_number: 42 (from evolutionData.dex: 42)
22:23:46.992 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 31 (common): 23832
22:23:46.992 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized golbat (Lvl 31) with 23832 XP
22:23:46.992 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Golbat (ID: mayblc4e6uuc91e): 42
22:23:46.993 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Golbat with 23832 XP (Level 31) from _experience
22:23:46.993 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:46.993 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 18 (common): 4665
22:23:46.993 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Zubat (Lvl 18) with 4665 XP
22:23:46.993 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: mayblb9gyrayt47): 41
22:23:46.993 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 4665 XP (Level 18) from _experience
22:23:46.993 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:46.993 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 21 (common): 7408
22:23:46.993 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized zubat (Lvl 21) with 7408 XP
22:23:46.994 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: maydxu10kl7j1v5): 41
22:23:46.994 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 7408 XP (Level 21) from _experience
22:23:46.994 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:46.994 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 4 (common): 51
22:23:46.994 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized zubat (Lvl 4) with 51 XP
22:23:46.994 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: maydxuasq8d0u3b): 41
22:23:46.994 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 51 XP (Level 4) from _experience
22:23:46.994 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:46.995 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 18 (common): 4665
22:23:46.995 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized zubat (Lvl 18) with 4665 XP
22:23:46.995 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: maydxwesg9svcec): 41
22:23:46.995 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 4665 XP (Level 18) from _experience
22:23:46.995 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Amonitas with dex_number: 138 (from evolutionData.dex: 138)
22:23:46.995 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 5 (rare): 350
22:23:46.995 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Amonitas (Lvl 5) with 350 XP
22:23:46.995 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Amonitas (ID: ma6yddcskyogc86s0): 138
22:23:46.996 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Amonitas with 350 XP (Level 5) from _experience
22:23:46.996 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:46.996 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 13 (common): 1757
22:23:46.996 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Zubat (Lvl 13) with 1757 XP
22:23:46.996 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: mayuw3kjelcr79s): 41
22:23:46.996 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 1757 XP (Level 13) from _experience
22:23:46.996 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:46.996 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 6 (common): 172
22:23:46.996 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized zubat (Lvl 6) with 172 XP
22:23:46.997 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: mayuw2ksp8mpr65): 41
22:23:46.997 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 172 XP (Level 6) from _experience
22:23:46.997 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:46.997 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 10 (common): 800
22:23:46.997 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Zubat (Lvl 10) with 800 XP
22:23:46.997 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: mayz5ldl4n2g9pj): 41
22:23:46.997 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 800 XP (Level 10) from _experience
22:23:46.997 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:46.997 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 11 (common): 1064
22:23:46.997 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Zubat (Lvl 11) with 1064 XP
22:23:46.998 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: mayz5lj7t7kwx0c): 41
22:23:46.998 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 1064 XP (Level 11) from _experience
22:23:46.998 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:46.998 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 5 (common): 100
22:23:46.998 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Zubat (Lvl 5) with 100 XP
22:23:46.998 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: mayz5gnzd80pxa4): 41
22:23:46.998 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 100 XP (Level 5) from _experience
22:23:46.998 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:46.998 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 6 (common): 172
22:23:46.999 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Zubat (Lvl 6) with 172 XP
22:23:46.999 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: maz19xeu9fc13t1): 41
22:23:46.999 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 172 XP (Level 6) from _experience
22:23:46.999 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:46.999 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 3 (common): 21
22:23:46.999 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Zubat (Lvl 3) with 21 XP
22:23:46.999 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: maz19srrfpyxkce): 41
22:23:46.999 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 21 XP (Level 3) from _experience
22:23:46.999 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:47.000 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 6 (common): 172
22:23:47.000 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Zubat (Lvl 6) with 172 XP
22:23:47.000 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: maz96mvjseugbee): 41
22:23:47.000 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 172 XP (Level 6) from _experience
22:23:47.000 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:47.000 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 7 (common): 274
22:23:47.000 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Zubat (Lvl 7) with 274 XP
22:23:47.000 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: maz96qnzraa33di): 41
22:23:47.000 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 274 XP (Level 7) from _experience
22:23:47.001 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:47.001 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 5 (common): 100
22:23:47.001 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized zubat (Lvl 5) with 100 XP
22:23:47.001 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: maz96rhp048mujv): 41
22:23:47.001 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 100 XP (Level 5) from _experience
22:23:47.001 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:47.001 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 12 (common): 1382
22:23:47.001 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Zubat (Lvl 12) with 1382 XP
22:23:47.001 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: mazadkq9zlxaxpw): 41
22:23:47.002 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 1382 XP (Level 12) from _experience
22:23:47.002 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:47.002 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 5 (common): 100
22:23:47.002 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Zubat (Lvl 5) with 100 XP
22:23:47.002 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: mazc1yx7e96tmlb): 41
22:23:47.002 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 100 XP (Level 5) from _experience
22:23:47.002 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Safcon with dex_number: 11 (from evolutionData.dex: 11)
22:23:47.002 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 8 (common): 409
22:23:47.002 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Safcon (Lvl 8) with 409 XP
22:23:47.003 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Safcon (ID: mazhahfwd4kvwtr): 11
22:23:47.003 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Safcon with 409 XP (Level 8) from _experience
22:23:47.003 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Habitak with dex_number: 21 (from evolutionData.dex: 21)
22:23:47.003 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 5 (common): 100
22:23:47.003 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Habitak (Lvl 5) with 100 XP
22:23:47.003 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Habitak (ID: mazhaywaqj88snh): 21
22:23:47.003 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Habitak with 100 XP (Level 5) from _experience
22:23:47.003 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Bluzuk with dex_number: 48 (from evolutionData.dex: 48)
22:23:47.003 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Bluzuk (ID: mazhahf02eu4m5p): 48
22:23:47.004 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Bluzuk with 0 XP (Level 1) from _experience
22:23:47.004 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon koffing with dex_number: 109 (from evolutionData.dex: 109)
22:23:47.004 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 23 (common): 9733
22:23:47.004 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized koffing (Lvl 23) with 9733 XP
22:23:47.004 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Smogon (ID: mazi1y1x12mjibw): 109
22:23:47.004 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Smogon with 9733 XP (Level 23) from _experience
22:23:47.004 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Smogon with dex_number: 109 (from evolutionData.dex: 109)
22:23:47.004 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 8 (common): 409
22:23:47.005 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Smogon (Lvl 8) with 409 XP
22:23:47.005 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Smogon (ID: mazilyf88pc6puu): 109
22:23:47.005 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Smogon with 409 XP (Level 8) from _experience
22:23:47.005 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Smogon with dex_number: 109 (from evolutionData.dex: 109)
22:23:47.005 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 7 (common): 274
22:23:47.005 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Smogon (Lvl 7) with 274 XP
22:23:47.005 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Smogon (ID: mazim30dd7z2ikh): 109
22:23:47.005 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Smogon with 274 XP (Level 7) from _experience
22:23:47.005 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Paras with dex_number: 46 (from evolutionData.dex: 46)
22:23:47.006 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Paras (ID: mazibxsyzzz9ixd): 46
22:23:47.006 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Paras with 0 XP (Level 1) from _experience
22:23:47.006 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Sichlor with dex_number: 123 (from evolutionData.dex: 123)
22:23:47.006 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 5 (scarce): 100
22:23:47.006 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Sichlor (Lvl 5) with 100 XP
22:23:47.006 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Sichlor (ID: mazi1y2jn825fmz): 123
22:23:47.006 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Sichlor with 100 XP (Level 5) from _experience
22:23:47.006 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon weedle with dex_number: 13 (from evolutionData.dex: 13)
22:23:47.006 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 6 (common): 172
22:23:47.007 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized weedle (Lvl 6) with 172 XP
22:23:47.007 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Hornliu (ID: mazi1y34wewprbb): 13
22:23:47.007 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Hornliu with 190 XP (Level 6) from _experience
22:23:47.007 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Sichlor with dex_number: 123 (from evolutionData.dex: 123)
22:23:47.007 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 5 (scarce): 100
22:23:47.007 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Sichlor (Lvl 5) with 100 XP
22:23:47.007 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Sichlor (ID: mazily8l41b8hyy): 123
22:23:47.007 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Sichlor with 100 XP (Level 5) from _experience
22:23:47.007 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Raupy with dex_number: 10 (from evolutionData.dex: 10)
22:23:47.008 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Raupy (ID: mazilybd06snoa7): 10
22:23:47.008 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Raupy with 0 XP (Level 1) from _experience
22:23:47.008 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Pinsir with dex_number: 127 (from evolutionData.dex: 127)
22:23:47.008 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 7 (scarce): 274
22:23:47.008 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Pinsir (Lvl 7) with 274 XP
22:23:47.008 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Pinsir (ID: mazily9gzcj961s): 127
22:23:47.008 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Pinsir with 274 XP (Level 7) from _experience
22:23:47.008 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon venonat with dex_number: 48 (from evolutionData.dex: 48)
22:23:47.008 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 9 (common): 583
22:23:47.008 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized venonat (Lvl 9) with 583 XP
22:23:47.009 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Bluzuk (ID: mazily8vgzmrw2d): 48
22:23:47.009 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Bluzuk with 583 XP (Level 9) from _experience
22:23:47.009 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon venonat with dex_number: 48 (from evolutionData.dex: 48)
22:23:47.009 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 20 (common): 6400
22:23:47.009 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized venonat (Lvl 20) with 6400 XP
22:23:47.009 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Bluzuk (ID: mazily96ga5n9yq): 48
22:23:47.009 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Bluzuk with 6400 XP (Level 20) from _experience
22:23:47.009 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Hornliu with dex_number: 13 (from evolutionData.dex: 13)
22:23:47.009 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 6 (common): 172
22:23:47.010 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Hornliu (Lvl 6) with 172 XP
22:23:47.010 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Hornliu (ID: mazilya5vsmbxc8): 13
22:23:47.010 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Hornliu with 172 XP (Level 6) from _experience
22:23:47.010 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Bluzuk with dex_number: 48 (from evolutionData.dex: 48)
22:23:47.010 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Bluzuk (ID: mazilyasiq5g20y): 48
22:23:47.010 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Bluzuk with 0 XP (Level 1) from _experience
22:23:47.010 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Bibor with dex_number: 15 (from evolutionData.dex: 15)
22:23:47.010 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 10 (common): 800
22:23:47.010 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Bibor (Lvl 10) with 800 XP
22:23:47.011 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Bibor (ID: mazilyae3rb3myd): 15
22:23:47.011 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Bibor with 800 XP (Level 10) from _experience
22:23:47.011 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Habitak with dex_number: 21 (from evolutionData.dex: 21)
22:23:47.011 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 3 (common): 21
22:23:47.011 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Habitak (Lvl 3) with 21 XP
22:23:47.011 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Habitak (ID: mazi286jax703d2): 21
22:23:47.011 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Habitak with 21 XP (Level 3) from _experience
22:23:47.011 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:47.011 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 9 (common): 583
22:23:47.012 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Zubat (Lvl 9) with 583 XP
22:23:47.012 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: mazriswi35du922): 41
22:23:47.012 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 583 XP (Level 9) from _experience
22:23:47.012 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:47.012 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 14 (common): 2195
22:23:47.012 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Zubat (Lvl 14) with 2195 XP
22:23:47.012 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: mb0ag70xqbqpp04): 41
22:23:47.012 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 2195 XP (Level 14) from _experience
22:23:47.013 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:47.013 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 6 (common): 172
22:23:47.013 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Zubat (Lvl 6) with 172 XP
22:23:47.013 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: mb0h4h53pjk5fl4): 41
22:23:47.013 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 172 XP (Level 6) from _experience
22:23:47.013 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:47.013 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: mb18pdyjesvu4o5): 41
22:23:47.013 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 0 XP (Level 1) from _experience
22:23:47.013 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:47.014 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 5 (common): 100
22:23:47.014 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized zubat (Lvl 5) with 100 XP
22:23:47.014 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: mb18pdyfgr105z7): 41
22:23:47.014 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 100 XP (Level 5) from _experience
22:23:47.014 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:47.014 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 18 (common): 4665
22:23:47.014 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized zubat (Lvl 18) with 4665 XP
22:23:47.014 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: mb18pdyo9tkjer4): 41
22:23:47.014 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 4665 XP (Level 18) from _experience
22:23:47.015 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:47.015 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 6 (common): 172
22:23:47.015 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Zubat (Lvl 6) with 172 XP
22:23:47.015 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: mb1abgt0e3t2ei8): 41
22:23:47.015 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 172 XP (Level 6) from _experience
22:23:47.015 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:47.015 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 8 (common): 409
22:23:47.015 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized zubat (Lvl 8) with 409 XP
22:23:47.015 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: mb25pekso0gv7m6): 41
22:23:47.016 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 409 XP (Level 8) from _experience
22:23:47.016 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:47.016 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 4 (common): 51
22:23:47.016 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Zubat (Lvl 4) with 51 XP
22:23:47.016 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: mb25pekrhwqyt50): 41
22:23:47.016 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 51 XP (Level 4) from _experience
22:23:47.016 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:47.016 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 10 (common): 800
22:23:47.017 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized zubat (Lvl 10) with 800 XP
22:23:47.017 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: mb25pekqosruxrl): 41
22:23:47.017 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 800 XP (Level 10) from _experience
22:23:47.017 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon golbat with dex_number: 42 (from evolutionData.dex: 42)
22:23:47.017 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 24 (common): 11059
22:23:47.017 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized golbat (Lvl 24) with 11059 XP
22:23:47.017 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Golbat (ID: mb25pekr4soxm4y): 42
22:23:47.017 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Golbat with 11059 XP (Level 24) from _experience
22:23:47.017 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon golbat with dex_number: 42 (from evolutionData.dex: 42)
22:23:47.018 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 24 (common): 11059
22:23:47.018 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized golbat (Lvl 24) with 11059 XP
22:23:47.018 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Golbat (ID: mb2og4sc4kzz9nu): 42
22:23:47.018 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Golbat with 11059 XP (Level 24) from _experience
22:23:47.018 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:47.018 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 6 (common): 172
22:23:47.018 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Zubat (Lvl 6) with 172 XP
22:23:47.018 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: mb2og4seu8dlinm): 41
22:23:47.019 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 172 XP (Level 6) from _experience
22:23:47.019 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:47.019 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 9 (common): 583
22:23:47.019 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized zubat (Lvl 9) with 583 XP
22:23:47.019 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: mb3ezxgxcxcg2wd): 41
22:23:47.019 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 583 XP (Level 9) from _experience
22:23:47.019 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:47.019 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 2 (common): 6
22:23:47.020 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized Zubat (Lvl 2) with 6 XP
22:23:47.020 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: mb3h9jl1q3u1zxp): 41
22:23:47.020 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 6 XP (Level 2) from _experience
22:23:47.020 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon Zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:47.020 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: mb3h9jl3477e7k9): 41
22:23:47.020 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 0 XP (Level 1) from _experience
22:23:47.020 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:47.021 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 18 (common): 4665
22:23:47.021 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized zubat (Lvl 18) with 4665 XP
22:23:47.021 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: mb3rw7jreu11ozh): 41
22:23:47.021 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 4665 XP (Level 18) from _experience
22:23:47.021 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:47.021 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 19 (common): 5487
22:23:47.021 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized zubat (Lvl 19) with 5487 XP
22:23:47.022 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: mb3rw7jm7hdmbnb): 41
22:23:47.022 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 5487 XP (Level 19) from _experience
22:23:47.022 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon zubat with dex_number: 42 (from evolutionData.dex: 42)
22:23:47.022 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 27 (common): 15746
22:23:47.022 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized zubat (Lvl 27) with 15746 XP
22:23:47.022 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Golbat (ID: mb412wf93u91m4m): 42
22:23:47.022 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Golbat with 15746 XP (Level 27) from _experience
22:23:47.022 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:47.023 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 19 (common): 5487
22:23:47.023 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized zubat (Lvl 19) with 5487 XP
22:23:47.023 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for Zubat (ID: mb412wf4j056uvj): 41
22:23:47.023 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded Zubat with 5487 XP (Level 19) from _experience
22:23:47.023 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon golbat with dex_number: 42 (from evolutionData.dex: 42)
22:23:47.023 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 35 (common): 34300
22:23:47.023 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized golbat (Lvl 35) with 34300 XP
22:23:47.023 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for golbat (ID: mb41z89nnxjp0r7): 42
22:23:47.023 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded golbat with 35979 XP (Level 35) from _experience
22:23:47.023 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:47.024 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 18 (common): 4665
22:23:47.024 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized zubat (Lvl 18) with 4665 XP
22:23:47.024 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for zubat (ID: mb42spznro63lgb): 41
22:23:47.024 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded zubat with 4665 XP (Level 18) from _experience
22:23:47.024 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon zubat with dex_number: 42 (from evolutionData.dex: 42)
22:23:47.024 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 25 (common): 12500
22:23:47.024 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized zubat (Lvl 25) with 12500 XP
22:23:47.024 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for golbat (ID: mb4lk96u503bkdq): 42
22:23:47.024 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded golbat with 12500 XP (Level 25) from _experience
22:23:47.024 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:47.024 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 19 (common): 5487
22:23:47.025 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized zubat (Lvl 19) with 5487 XP
22:23:47.025 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for zubat (ID: mb4lk96oh2ldspc): 41
22:23:47.025 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded zubat with 5487 XP (Level 19) from _experience
22:23:47.025 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon jigglypuff with dex_number: 39 (from evolutionData.dex: 39)
22:23:47.025 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 5 (common): 100
22:23:47.025 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized jigglypuff (Lvl 5) with 100 XP
22:23:47.025 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for jigglypuff (ID: mb4qo9iqey21sli): 39
22:23:47.025 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded jigglypuff with 100 XP (Level 5) from _experience
22:23:47.025 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon jigglypuff with dex_number: 39 (from evolutionData.dex: 39)
22:23:47.025 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for jigglypuff (ID: mb4qokyitmumehz): 39
22:23:47.026 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded jigglypuff with 0 XP (Level 1) from _experience
22:23:47.026 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon doduo with dex_number: 84 (from evolutionData.dex: 84)
22:23:47.026 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 7 (common): 274
22:23:47.026 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized doduo (Lvl 7) with 274 XP
22:23:47.026 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for doduo (ID: mb4qo9ioad4acwb): 84
22:23:47.026 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded doduo with 274 XP (Level 7) from _experience
22:23:47.026 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon jigglypuff with dex_number: 39 (from evolutionData.dex: 39)
22:23:47.026 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 9 (common): 583
22:23:47.026 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized jigglypuff (Lvl 9) with 583 XP
22:23:47.026 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for jigglypuff (ID: mb4qokygnokdpwn): 39
22:23:47.027 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded jigglypuff with 583 XP (Level 9) from _experience
22:23:47.027 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon jigglypuff with dex_number: 39 (from evolutionData.dex: 39)
22:23:47.027 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 7 (common): 274
22:23:47.027 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized jigglypuff (Lvl 7) with 274 XP
22:23:47.027 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for jigglypuff (ID: mb4qo9ij5lfv9xs): 39
22:23:47.027 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded jigglypuff with 274 XP (Level 7) from _experience
22:23:47.027 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon jigglypuff with dex_number: 39 (from evolutionData.dex: 39)
22:23:47.027 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 12 (common): 1382
22:23:47.027 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized jigglypuff (Lvl 12) with 1382 XP
22:23:47.028 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for jigglypuff (ID: mb4qokyrfzm9d8x): 39
22:23:47.028 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded jigglypuff with 1382 XP (Level 12) from _experience
22:23:47.028 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:47.028 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 8 (common): 409
22:23:47.028 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized zubat (Lvl 8) with 409 XP
22:23:47.028 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for zubat (ID: mb5hoeasx8yyor6): 41
22:23:47.028 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded zubat with 409 XP (Level 8) from _experience
22:23:47.028 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:47.029 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 6 (common): 172
22:23:47.029 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized zubat (Lvl 6) with 172 XP
22:23:47.029 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for zubat (ID: mb5krzamgfzvq5j): 41
22:23:47.029 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded zubat with 172 XP (Level 6) from _experience
22:23:47.029 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:47.029 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 14 (common): 2195
22:23:47.029 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized zubat (Lvl 14) with 2195 XP
22:23:47.029 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for zubat (ID: mb6izeitaatzk5u): 41
22:23:47.029 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded zubat with 2195 XP (Level 14) from _experience
22:23:47.029 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon zubat with dex_number: 41 (from evolutionData.dex: 41)
22:23:47.030 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 7 (common): 274
22:23:47.030 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized zubat (Lvl 7) with 274 XP
22:23:47.030 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for zubat (ID: mb6uuamu28czx1r): 41
22:23:47.030 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded zubat with 274 XP (Level 7) from _experience
22:23:47.030 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon golbat with dex_number: 42 (from evolutionData.dex: 42)
22:23:47.030 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 25 (common): 12500
22:23:47.030 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized golbat (Lvl 25) with 12500 XP
22:23:47.030 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for golbat (ID: mb6y9n1kraapm28): 42
22:23:47.030 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded golbat with 14044 XP (Level 25) from _experience
22:23:47.030 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon wigglytuff with dex_number: 39 (from evolutionData.dex: 39)
22:23:47.031 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 13 (common): 1757
22:23:47.031 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized wigglytuff (Lvl 13) with 1757 XP
22:23:47.031 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for jigglypuff (ID: mb6yqollp0t4k6w): 39
22:23:47.031 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded jigglypuff with 1757 XP (Level 13) from _experience
22:23:47.031 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon pidgey with dex_number: 16 (from evolutionData.dex: 16)
22:23:47.031 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 17 (common): 3930
22:23:47.031 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized pidgey (Lvl 17) with 3930 XP
22:23:47.031 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for pidgey (ID: mb7mufq4h6cdurk): 16
22:23:47.031 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded pidgey with 3930 XP (Level 17) from _experience
22:23:47.032 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon pidgey with dex_number: 17 (from evolutionData.dex: 17)
22:23:47.032 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 26 (common): 14060
22:23:47.032 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized pidgey (Lvl 26) with 14060 XP
22:23:47.032 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for pidgeotto (ID: mb7mufpshqcjlz4): 17
22:23:47.032 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded pidgeotto with 14060 XP (Level 26) from _experience
22:23:47.032 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon zubat with dex_number: 42 (from evolutionData.dex: 42)
22:23:47.032 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 26 (common): 14060
22:23:47.032 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized zubat (Lvl 26) with 14060 XP
22:23:47.032 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for golbat (ID: mb8gd6y8m9v9ep6): 42
22:23:47.032 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded golbat with 14060 XP (Level 26) from _experience
22:23:47.032 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon zubat with dex_number: 42 (from evolutionData.dex: 42)
22:23:47.033 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 27 (common): 15746
22:23:47.033 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized zubat (Lvl 27) with 15746 XP
22:23:47.033 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for golbat (ID: mb8gd6yfisnpobz): 42
22:23:47.033 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded golbat with 15746 XP (Level 27) from _experience
22:23:47.033 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon zubat with dex_number: 42 (from evolutionData.dex: 42)
22:23:47.033 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 23 (common): 9733
22:23:47.033 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized zubat (Lvl 23) with 9733 XP
22:23:47.033 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for golbat (ID: mb97701e58rbusk): 42
22:23:47.033 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded golbat with 9733 XP (Level 23) from _experience
22:23:47.034 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon arbok with dex_number: 24 (from evolutionData.dex: 24)
22:23:47.034 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 27 (common): 15746
22:23:47.034 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized arbok (Lvl 27) with 15746 XP
22:23:47.034 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for arbok (ID: mbauhfp7yyt237c): 24
22:23:47.034 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded arbok with 16490 XP (Level 27) from _experience
22:23:47.034 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon doduo with dex_number: 84 (from evolutionData.dex: 84)
22:23:47.034 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 17 (common): 3930
22:23:47.034 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized doduo (Lvl 17) with 3930 XP
22:23:47.034 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for doduo (ID: mbcpg2e4asjj2tn): 84
22:23:47.034 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded doduo with 3930 XP (Level 17) from _experience
22:23:47.034 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon clefairy with dex_number: 35 (from evolutionData.dex: 35)
22:23:47.034 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 17 (common): 3930
22:23:47.035 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized clefairy (Lvl 17) with 3930 XP
22:23:47.035 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for clefairy (ID: mbccv5zd51jxzz1): 35
22:23:47.035 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded clefairy with 3930 XP (Level 17) from _experience
22:23:47.035 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon jigglypuff with dex_number: 39 (from evolutionData.dex: 39)
22:23:47.035 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 21 (common): 7408
22:23:47.035 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized jigglypuff (Lvl 21) with 7408 XP
22:23:47.035 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for jigglypuff (ID: mbccv5z5kvb0xdh): 39
22:23:47.035 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded jigglypuff with 7408 XP (Level 21) from _experience
22:23:47.035 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon magmar with dex_number: 126 (from evolutionData.dex: 126)
22:23:47.036 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 11 (scarce): 1064
22:23:47.036 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized magmar (Lvl 11) with 1064 XP
22:23:47.036 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:93] Setting dex_number for magmar (ID: mbhwa6hjpc7e1nm): 126
22:23:47.036 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:132] Loaded magmar with 1064 XP (Level 11) from _experience
22:23:47.036 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-manager.js:56] Sample of loaded Pokemon:
22:23:47.036 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-manager.js:59]   Owei (ID: ma4h8c95q1mw6h53q): Level 5, XP 100
22:23:47.036 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-manager.js:59]   Pikachu (ID: maedl8kncy8tkrc): Level 5, XP 100
22:23:47.036 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-manager.js:59]   Rattikarl (ID: ma8fh5klstpqlzue9): Level 5, XP 100
22:23:47.036 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-manager.js:59]   Enton (ID: ma8f6qpn3l8sze7ab): Level 12, XP 1540
22:23:47.036 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-manager.js:59]   Pikachu (ID: maj8kbxuxq7q8vt): Level 2, XP 6
22:23:47.036 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-manager.js:66] Loaded 6 Pokemon in team
22:23:47.036 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-manager.js:101] Pokemon already exist in new storage, skipping migration
22:23:47.037 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-manager.js:76] Sample of Pokemon after validation:
22:23:47.037 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-manager.js:79]   Owei (ID: ma4h8c95q1mw6h53q): Level 5, XP 100, dex_number: 102
22:23:47.037 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-manager.js:79]   Pikachu (ID: maedl8kncy8tkrc): Level 5, XP 100, dex_number: 25
22:23:47.037 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-manager.js:79]   Rattikarl (ID: ma8fh5klstpqlzue9): Level 5, XP 100, dex_number: 20
22:23:47.037 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-manager.js:79]   Enton (ID: ma8f6qpn3l8sze7ab): Level 12, XP 1540, dex_number: 54
22:23:47.037 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-manager.js:79]   Pikachu (ID: maj8kbxuxq7q8vt): Level 2, XP 6, dex_number: 25
22:23:47.037 Capacitor/Console        I  File: https://localhost/utils/logger.js - Line 135 - Msg: [https://localhost/main.js:47] Pokemon manager initialized
22:23:47.041 Capacitor                D  Handling local request: https://localhost/fonts/Poppins-Bold.ttf
22:23:47.041 Capacitor                D  Handling local request: https://localhost/fonts/Poppins-Regular.ttf
22:23:47.049 Capacitor/Console        I  File: https://localhost/services/player-renderer.js - Line 45 - Msg: Sprite base path: ./src/PlayerSprites/male/
22:23:47.049 Capacitor                D  Handling local request: https://localhost/styles/player.css
22:23:47.049 Capacitor/Console        I  File: https://localhost/services/player-renderer.js - Line 72 - Msg: Player size: 16 x 24
22:23:47.049 Capacitor/Console        I  File: https://localhost/services/player-renderer.js - Line 76 - Msg: Using icon size: 16,24
22:23:47.050 Capacitor/Console        I  File: https://localhost/services/player-renderer.js - Line 99 - Msg: Player sprite created at 51.96 7.62
22:23:47.050 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/main.js:67] Player sprite initialized at map center: 51.96,7.62
22:23:47.051 Capacitor/Console        I  File: https://localhost/utils/logger.js - Line 135 - Msg: [https://localhost/main.js:79] Initializing time-event system...
22:23:47.053 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/capacitor/time-events.js:105] Loaded 0 Pokemon spawns from storage
22:23:47.053 Capacitor/Console        I  File: https://localhost/utils/logger.js - Line 135 - Msg: [https://localhost/main.js:185] New time slot or no stored spawns, will generate new spawns
22:23:47.053 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/capacitor/time-events.js:243] Hour change timer is disabled - time slots only change on app restart
22:23:47.054 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/ui/FabManager.js:53] Created new FAB bar with grid layout
22:23:47.055 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/ui/FabManager.js:104] Created FAB button with ID submenu-fab at grid position 4
22:23:47.055 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/ui/FabManager.js:104] Created FAB button with ID battle-fab at grid position 1
22:23:47.055 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/ui/FabManager.js:104] Created FAB button with ID debug-fab at grid position 3
22:23:47.055 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/ui/FabManager.js:104] Created FAB button with ID center-fab at grid position 2
22:23:47.056 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/ui/FabSubmenuManager.js:33] FabSubmenuManager initialized
22:23:47.056 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/ui/FabManager.js:30] FabManager initialized
22:23:47.056 Capacitor/Console        I  File: https://localhost/utils/logger.js - Line 135 - Msg: [https://localhost/capacitor/geolocation.js:293] Requesting Capacitor location permissions
22:23:47.059 Capacitor/Plugin         V  To native (Capacitor plugin): callbackId: 2860739, pluginId: Geolocation, methodName: requestPermissions
22:23:47.059 Capacitor                V  callback: 2860739, pluginId: Geolocation, methodName: requestPermissions, methodData: {}
22:23:47.062 Capacitor                D  Handling local request: https://localhost/icons/materialicons/menu.svg
22:23:47.064 Capacitor                D  Handling local request: https://localhost/icons/materialicons/swords.svg
22:23:47.064 Capacitor                D  Handling local request: https://localhost/icons/materialicons/bug.svg
22:23:47.065 Capacitor                D  Handling local request: https://localhost/icons/materialicons/mylocation.svg
22:23:47.066 Capacitor                D  Handling local request: https://localhost/icons/materialicons/checklist.svg
22:23:47.067 Capacitor                D  Handling local request: https://localhost/icons/materialicons/checkball.svg
22:23:47.067 Capacitor                D  Handling local request: https://localhost/icons/materialicons/eye.svg
22:23:47.067 Capacitor                D  Handling local request: https://localhost/icons/materialicons/close.svg
22:23:47.080 Capacitor/Console        I  File: https://localhost/utils/logger.js - Line 135 - Msg: [https://localhost/capacitor/geolocation.js:338] Capacitor location permissions after request: granted
22:23:47.081 Capacitor/Plugin         V  To native (Capacitor plugin): callbackId: 2860740, pluginId: KeepAwake, methodName: keepAwake
22:23:47.081 Capacitor                V  callback: 2860740, pluginId: KeepAwake, methodName: keepAwake, methodData: {}
22:23:47.082 DecorView                I  notifyKeepScreenOnChanged: keepScreenOn=true
22:23:47.088 ViewRootIm...nActivity]  I  performTraversals params={(0,0)(fillxfill) sim={adjust=resize} layoutInDisplayCutoutMode=shortEdges ty=BASE_APPLICATION wanim=0x1030309
  fl=81810180
  pfl=12020040
  bhv=DEFAULT
  fitSides= naviIconColor=0}
22:23:47.088 ViewRootIm...nActivity]  I  performTraversals mFirst=false windowShouldResize=false viewVisibilityChanged=false mForceNextWindowRelayout=false params={(0,0)(fillxfill) sim={adjust=resize} layoutInDisplayCutoutMode=shortEdges ty=BASE_APPLICATION wanim=0x1030309
  fl=81810180
  pfl=12020040
  bhv=DEFAULT
  fitSides= naviIconColor=0}
22:23:47.092 ViewRootIm...nActivity]  I  updateBlastSurfaceIfNeeded mBlastBufferQueue=0xb400007c3b287680 isSameSurfaceControl=true
22:23:47.093 BLASTBufferQueue         I  update, w= 1080 h= 2400 mName = ViewRootImpl@3c7f832[MainActivity] mNativeObject= 0xb400007c3b287680 sc.mNativeObject= 0xb400007c9e6f4720 format= -1 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:2898 android.view.ViewRootImpl.relayoutWindow:9847 android.view.ViewRootImpl.performTraversals:3884 android.view.ViewRootImpl.doTraversal:3116 android.view.ViewRootImpl$TraversalRunnable.run:10885 android.view.Choreographer$CallbackRecord.run:1301 
22:23:47.093 ViewRootIm...nActivity]  I  Relayout returned: old=(0,0,1080,2400) new=(0,0,1080,2400) req=(1080,2400)0 dur=4 res=0x0 s={true 0xb400007c9e5d1800} ch=false seqId=0
22:23:47.103 Capacitor/Console        I  File:  - Line 334 - Msg: undefined
22:23:47.103 Capacitor/Console        I  File: https://localhost/utils/logger.js - Line 135 - Msg: [https://localhost/capacitor/keep-awake.js:20] Screen will stay awake
22:23:47.103 Capacitor/Console        I  File: https://localhost/utils/logger.js - Line 135 - Msg: [https://localhost/capacitor/geolocation.js:58] Using Capacitor Geolocation for position watching
22:23:47.104 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/capacitor/app.js:198] Registering main map back button handler
22:23:47.104 Capacitor/Console        I  File: https://localhost/utils/logger.js - Line 135 - Msg: [https://localhost/main.js:237] App initialized successfully
22:23:47.104 Capacitor/Console        I  File: https://localhost/utils/logger.js - Line 135 - Msg: [https://localhost/capacitor/geolocation.js:101] Started Capacitor position watch with ID: 2860741
22:23:47.112 Capacitor/Plugin         V  To native (Capacitor plugin): callbackId: 2860741, pluginId: Geolocation, methodName: watchPosition
22:23:47.112 Capacitor                V  callback: 2860741, pluginId: Geolocation, methodName: watchPosition, methodData: {"enableHighAccuracy":true,"timeout":10000,"maximumAge":0}
22:23:47.114 Capacitor/Plugin         V  To native (Capacitor plugin): callbackId: 2860742, pluginId: App, methodName: removeAllListeners
22:23:47.115 Capacitor                V  callback: 2860742, pluginId: App, methodName: removeAllListeners, methodData: {}
22:23:47.115 Capacitor/Plugin         V  To native (Capacitor plugin): callbackId: 2860743, pluginId: App, methodName: addListener
22:23:47.115 Capacitor                V  callback: 2860743, pluginId: App, methodName: addListener, methodData: {"eventName":"backButton"}
22:23:47.121 SensorManager            D  registerListener :: 151, Game Rotation Vector  Non-wakeup, 16666, 0, 
22:23:47.127 Capacitor/Console        I  File:  - Line 334 - Msg: undefined
22:23:48.307 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/capacitor/geolocation.js:96] Capacitor Geolocation update: lat=50.4859782, lng=10.8340414, accuracy=15.319
22:23:48.308 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/main.js:302] Initial spawn - generating new Pokemon
22:23:48.309 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:525] Created pokedex snapshot with 151 entries
22:23:48.309 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:557] Starting parallel data collection for 20 Pokemon with grid distribution...
22:23:48.309 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:695] Created pokedex snapshot for landuse spawning with 151 entries
22:23:48.310 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/main.js:351] Spawning test trainer...
22:23:48.312 Capacitor                D  Handling local request: https://localhost/trainerTypes.json
22:23:48.315 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/pokemon-grid.js:176] Grid cell (2550,18678) maps to chain index 5 of 78 (rarity: common)
22:23:48.315 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:643] Found base Pokemon for chain 6: pidgey (16)
22:23:48.315 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-manager.js:198] getTeamPokemon returning 6 Pokemon: Owei, Sichlor, Tragosso, Rossana, Evoli, arbok
22:23:48.316 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/spawn-levels.js:29] Average team level for spawns: 24 (from 6 Pokemon)
22:23:48.316 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/spawn-levels.js:94] Spawn level: 25 (team average 24 with deviation 1)
22:23:48.316 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon pidgey with dex_number: null (from evolutionData.dex: undefined)
22:23:48.316 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 25 (common): 12500
22:23:48.316 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized pidgey (Lvl 25) with 12500 XP
22:23:48.317 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/utils/pokemon-utils.js:43] Found Pokemon by exact name match: pidgey
22:23:48.317 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:428] Pokemon family for pidgey (chain 6): pidgey (Dex #16, Evo level: 18), pidgeotto (Dex #17, Evo level: 36), pidgeot (Dex #18, Evo level: null)
22:23:48.317 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:443] [getDisplayForm] Pokemon: id=mdhubgbfmh95q9n, base_name=pidgey, name=pidgey, level=25
22:23:48.317 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:449] [getDisplayForm] Family: {name: pidgey, dex: 16, evolution_level: 18}, {name: pidgeotto, dex: 17, evolution_level: 36}, {name: pidgeot, dex: 18, evolution_level: none}
22:23:48.317 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:467] Evolution by level: pidgey -> pidgeotto (Level: 25 >= 18)
22:23:48.318 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:491] [getDisplayForm] Current stage: name=pidgeotto, dex=17, evolution_level=36
22:23:48.320 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/trainer-spawner.js:31] Loaded 34 trainer types
22:23:48.320 Capacitor                D  Handling local request: https://localhost/src/NPCNames/NPCNames.json
22:23:48.323 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/trainer-spawner.js:38] Loaded 100 male and 100 female NPC names
22:23:48.324 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon nidoran-m with dex_number: null (from evolutionData.dex: undefined)
22:23:48.325 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 49 (common): 94119
22:23:48.325 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized nidoran-m (Lvl 49) with 94119 XP
22:23:48.325 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/utils/pokemon-utils.js:43] Found Pokemon by exact name match: nidoran-m
22:23:48.325 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:428] Pokemon family for nidoran-m (chain 13): nidoran-m (Dex #32, Evo level: 16), nidorino (Dex #33, Evo level: 32), nidoking (Dex #34, Evo level: null)
22:23:48.325 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:443] [getDisplayForm] Pokemon: id=mdhubgbohks7gho, base_name=nidoran-m, name=nidoran-m, level=49
22:23:48.325 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:449] [getDisplayForm] Family: {name: nidoran-m, dex: 32, evolution_level: 16}, {name: nidorino, dex: 33, evolution_level: 32}, {name: nidoking, dex: 34, evolution_level: none}
22:23:48.325 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:467] Evolution by level: nidoran-m -> nidorino (Level: 49 >= 16)
22:23:48.326 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:467] Evolution by level: nidorino -> nidoking (Level: 49 >= 32)
22:23:48.326 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:491] [getDisplayForm] Current stage: name=nidoking, dex=34, evolution_level=none
22:23:48.326 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon nidoran-m with dex_number: 32 (from evolutionData.dex: 32)
22:23:48.326 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 49 (common): 94119
22:23:48.326 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized nidoran-m (Lvl 49) with 94119 XP
22:23:48.326 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/trainer-spawner.js:249] Created trainer Pokemon: nidoking (Level 49) evolved from nidoran-m
22:23:48.327 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon jolteon with dex_number: null (from evolutionData.dex: undefined)
22:23:48.327 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 47 (common): 83058
22:23:48.327 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized jolteon (Lvl 47) with 83058 XP
22:23:48.327 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/utils/pokemon-utils.js:43] Found Pokemon by exact name match: jolteon
22:23:48.328 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:428] Pokemon family for jolteon (chain 67): eevee (Dex #133, Evo level: null), vaporeon (Dex #134, Evo level: null), jolteon (Dex #135, Evo level: null), flareon (Dex #136, Evo level: null)
22:23:48.328 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:443] [getDisplayForm] Pokemon: id=mdhubgbqyc48sa7, base_name=jolteon, name=jolteon, level=47
22:23:48.328 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:449] [getDisplayForm] Family: {name: eevee, dex: 133, evolution_level: none}, {name: vaporeon, dex: 134, evolution_level: none}, {name: jolteon, dex: 135, evolution_level: none}, {name: flareon, dex: 136, evolution_level: none}
22:23:48.328 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:491] [getDisplayForm] Current stage: name=eevee, dex=133, evolution_level=none
22:23:48.328 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon jolteon with dex_number: 135 (from evolutionData.dex: 135)
22:23:48.328 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 47 (rare): 106727
22:23:48.328 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized jolteon (Lvl 47) with 106727 XP
22:23:48.328 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/trainer-spawner.js:249] Created trainer Pokemon: eevee (Level 47) evolved from jolteon
22:23:48.329 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon weedle with dex_number: null (from evolutionData.dex: undefined)
22:23:48.329 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 55 (common): 133100
22:23:48.329 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized weedle (Lvl 55) with 133100 XP
22:23:48.330 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/utils/pokemon-utils.js:43] Found Pokemon by exact name match: weedle
22:23:48.330 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:428] Pokemon family for weedle (chain 5): weedle (Dex #13, Evo level: 7), kakuna (Dex #14, Evo level: 10), beedrill (Dex #15, Evo level: null)
22:23:48.330 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:443] [getDisplayForm] Pokemon: id=mdhubgbt3l3r3pr, base_name=weedle, name=weedle, level=55
22:23:48.330 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:449] [getDisplayForm] Family: {name: weedle, dex: 13, evolution_level: 7}, {name: kakuna, dex: 14, evolution_level: 10}, {name: beedrill, dex: 15, evolution_level: none}
22:23:48.330 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:467] Evolution by level: weedle -> kakuna (Level: 55 >= 7)
22:23:48.330 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:467] Evolution by level: kakuna -> beedrill (Level: 55 >= 10)
22:23:48.330 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:491] [getDisplayForm] Current stage: name=beedrill, dex=15, evolution_level=none
22:23:48.331 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon weedle with dex_number: 13 (from evolutionData.dex: 13)
22:23:48.331 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 55 (common): 133100
22:23:48.331 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized weedle (Lvl 55) with 133100 XP
22:23:48.331 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/trainer-spawner.js:249] Created trainer Pokemon: beedrill (Level 55) evolved from weedle
22:23:48.331 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon venusaur with dex_number: null (from evolutionData.dex: undefined)
22:23:48.332 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 52 (common): 112486
22:23:48.332 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized venusaur (Lvl 52) with 112486 XP
22:23:48.332 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/utils/pokemon-utils.js:43] Found Pokemon by exact name match: venusaur
22:23:48.333 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:428] Pokemon family for venusaur (chain 1): bulbasaur (Dex #1, Evo level: 16), ivysaur (Dex #2, Evo level: 32), venusaur (Dex #3, Evo level: null)
22:23:48.333 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:443] [getDisplayForm] Pokemon: id=mdhubgbvkv8ed6r, base_name=venusaur, name=venusaur, level=52
22:23:48.333 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:449] [getDisplayForm] Family: {name: bulbasaur, dex: 1, evolution_level: 16}, {name: ivysaur, dex: 2, evolution_level: 32}, {name: venusaur, dex: 3, evolution_level: none}
22:23:48.333 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:467] Evolution by level: bulbasaur -> ivysaur (Level: 52 >= 16)
22:23:48.333 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:467] Evolution by level: ivysaur -> venusaur (Level: 52 >= 32)
22:23:48.333 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:491] [getDisplayForm] Current stage: name=venusaur, dex=3, evolution_level=none
22:23:48.333 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon venusaur with dex_number: 3 (from evolutionData.dex: 3)
22:23:48.333 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 52 (starter): 140608
22:23:48.334 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized venusaur (Lvl 52) with 140608 XP
22:23:48.334 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/trainer-spawner.js:249] Created trainer Pokemon: venusaur (Level 52) evolved from venusaur
22:23:48.334 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon electabuzz with dex_number: null (from evolutionData.dex: undefined)
22:23:48.334 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 51 (common): 106120
22:23:48.334 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized electabuzz (Lvl 51) with 106120 XP
22:23:48.335 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/utils/pokemon-utils.js:43] Found Pokemon by exact name match: electabuzz
22:23:48.335 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:428] Pokemon family for electabuzz (chain 60): electabuzz (Dex #125, Evo level: null)
22:23:48.336 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:443] [getDisplayForm] Pokemon: id=mdhubgbx9jk9or9, base_name=electabuzz, name=electabuzz, level=51
22:23:48.336 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:449] [getDisplayForm] Family: {name: electabuzz, dex: 125, evolution_level: none}
22:23:48.336 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:491] [getDisplayForm] Current stage: name=electabuzz, dex=125, evolution_level=none
22:23:48.336 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon electabuzz with dex_number: 125 (from evolutionData.dex: 125)
22:23:48.337 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 51 (scarce): 106120
22:23:48.337 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized electabuzz (Lvl 51) with 106120 XP
22:23:48.337 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/trainer-spawner.js:249] Created trainer Pokemon: electabuzz (Level 51) evolved from electabuzz
22:23:48.337 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon venomoth with dex_number: null (from evolutionData.dex: undefined)
22:23:48.337 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 40 (common): 51200
22:23:48.337 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized venomoth (Lvl 40) with 51200 XP
22:23:48.338 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/utils/pokemon-utils.js:43] Found Pokemon by exact name match: venomoth
22:23:48.338 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:428] Pokemon family for venomoth (chain 20): venonat (Dex #48, Evo level: 31), venomoth (Dex #49, Evo level: null)
22:23:48.338 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:443] [getDisplayForm] Pokemon: id=mdhubgc0jgqbjab, base_name=venomoth, name=venomoth, level=40
22:23:48.338 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:449] [getDisplayForm] Family: {name: venonat, dex: 48, evolution_level: 31}, {name: venomoth, dex: 49, evolution_level: none}
22:23:48.338 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:467] Evolution by level: venonat -> venomoth (Level: 40 >= 31)
22:23:48.338 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:491] [getDisplayForm] Current stage: name=venomoth, dex=49, evolution_level=none
22:23:48.338 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon venomoth with dex_number: 49 (from evolutionData.dex: 49)
22:23:48.338 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 40 (common): 51200
22:23:48.339 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized venomoth (Lvl 40) with 51200 XP
22:23:48.339 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/trainer-spawner.js:249] Created trainer Pokemon: venomoth (Level 40) evolved from venomoth
22:23:48.339 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/trainer-spawner.js:154] Generated team of 6 Pokemon for trainer
22:23:48.339 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Trainer.js:32] Created trainer Paul (Scientist) with 6 Pokemon, average level 49
22:23:48.339 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Trainer.js:66] Set trainer Paul position to 50.48613560700899, 10.834443437806868
22:23:48.339 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/trainer-spawner.js:86] Spawned trainer: Paul (Scientist) at 50.48613560700899, 10.834443437806868
22:23:48.340 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-manager.js:198] getTeamPokemon returning 6 Pokemon: Owei, Sichlor, Tragosso, Rossana, Evoli, arbok
22:23:48.342 Capacitor                D  Handling local request: https://localhost/src/NPCSprites/trainers/Scientist_M1_Map.png
22:23:48.344 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/main.js:444] Updating player sprite at 50.4859782, 10.8340414, heading: 270
22:23:48.344 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/main.js:457] Player is static, distSinceLastMove: 0
22:23:48.344 Capacitor/Console        I  File: https://localhost/services/player-renderer.js - Line 246 - Msg: Loading sprite image: ./src/PlayerSprites/male/playerm_w (1).png
22:23:48.347 Capacitor                D  Handling local request: https://localhost/src/PlayerSprites/male/playerm_w%20(1).png
22:23:48.347 Capacitor                D  Handling local request: https://localhost/src/PlayerSprites/male/playerm_w%20(3).png
22:23:48.348 Capacitor                D  Handling local request: https://localhost/src/PlayerSprites/male/playerm_w%20(2).png
22:23:48.354 Capacitor/Console        I  File: https://localhost/services/player-renderer.js - Line 250 - Msg: Sprite image loaded successfully
22:23:48.552 e.gpspokemonapp          I  Compiler allocated 6133KB to compile void android.view.ViewRootImpl.performTraversals()
22:23:51.729 ViewRootIm...nActivity]  I  ViewPostIme pointer 0
22:23:51.730 e.gpspokemonapp          W  Accessing hidden method Landroid/view/MotionEvent;->getEventTimeNano()J (unsupported, reflection, allowed)
22:23:51.760 ViewRootIm...nActivity]  I  ViewPostIme pointer 1
22:23:52.128 ViewRootIm...nActivity]  I  ViewPostIme pointer 0
22:23:52.137 ProfileInstaller         D  Installing profile for com.example.gpspokemonapp
22:23:52.183 ViewRootIm...nActivity]  I  ViewPostIme pointer 1
22:23:52.719 ViewRootIm...nActivity]  I  ViewPostIme pointer 0
22:23:52.740 e.gpspokemonapp          W  Accessing hidden method Landroid/view/MotionEvent;->getHistoricalEventTimeNano(I)J (max-target-o, reflection, denied)
22:23:52.759 ViewRootIm...nActivity]  I  ViewPostIme pointer 1
22:23:53.360 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/capacitor/geolocation.js:96] Capacitor Geolocation update: lat=50.4859786, lng=10.8340412, accuracy=16.339
22:23:53.360 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/main.js:444] Updating player sprite at 50.4859786, 10.8340412, heading: 270
22:23:53.360 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/main.js:457] Player is static, distSinceLastMove: 0.04667451695228709
22:23:55.957 ViewRootIm...nActivity]  I  ViewPostIme pointer 0
22:23:55.975 ViewRootIm...nActivity]  I  ViewPostIme pointer 1
22:23:55.987 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/pokemon-grid.js:176] Grid cell (2549,18677) maps to chain index 35 of 78 (rarity: common)
22:23:55.988 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/pokemon-grid.js:176] Grid cell (2550,18677) maps to chain index 18 of 78 (rarity: common)
22:23:55.989 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/pokemon-grid.js:176] Grid cell (2551,18677) maps to chain index 39 of 78 (rarity: common)
22:23:55.990 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/pokemon-grid.js:176] Grid cell (2549,18678) maps to chain index 42 of 78 (rarity: common)
22:23:55.990 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/pokemon-grid.js:176] Grid cell (2550,18678) maps to chain index 5 of 78 (rarity: common)
22:23:55.991 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/pokemon-grid.js:176] Grid cell (2551,18678) maps to chain index 33 of 78 (rarity: common)
22:23:55.991 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/pokemon-grid.js:176] Grid cell (2549,18679) maps to chain index 21 of 78 (rarity: common)
22:23:55.992 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/pokemon-grid.js:176] Grid cell (2550,18679) maps to chain index 16 of 78 (rarity: common)
22:23:55.992 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/pokemon-grid.js:176] Grid cell (2551,18679) maps to chain index 2 of 78 (rarity: starter)
22:23:55.993 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/pokemon-grid.js:176] Grid cell (2549,18680) maps to chain index 56 of 78 (rarity: rare)
22:23:55.993 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/pokemon-grid.js:176] Grid cell (2550,18680) maps to chain index 55 of 78 (rarity: common)
22:23:55.994 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/pokemon-grid.js:176] Grid cell (2551,18680) maps to chain index 57 of 78 (rarity: scarce)
22:23:55.994 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/pokemon-grid.js:176] Grid cell (2549,18681) maps to chain index 16 of 78 (rarity: common)
22:23:55.995 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/pokemon-grid.js:176] Grid cell (2550,18681) maps to chain index 51 of 78 (rarity: common)
22:23:55.995 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/pokemon-grid.js:176] Grid cell (2551,18681) maps to chain index 43 of 78 (rarity: common)
22:23:55.995 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/ui/FabManager.js:164] Debug mode toggled: true
22:23:57.886 System                   W  A resource failed to call close. 
22:24:01.374 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/capacitor/geolocation.js:96] Capacitor Geolocation update: lat=50.4859798, lng=10.8340345, accuracy=13.754
22:24:01.374 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/main.js:444] Updating player sprite at 50.4859798, 10.8340345, heading: 270
22:24:01.375 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/main.js:457] Player is static, distSinceLastMove: 0.5195817693398714
22:24:10.228 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/capacitor/geolocation.js:96] Capacitor Geolocation update: lat=50.4859835, lng=10.8340465, accuracy=14.481
22:24:10.228 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/main.js:444] Updating player sprite at 50.4859835, 10.8340465, heading: 270
22:24:10.229 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/main.js:457] Player is static, distSinceLastMove: 0.6910187810402999
22:24:16.996 Capacitor/Console        I  File: https://localhost/overpass-landuse.js - Line 132 - Msg: [OVERPASS-LANDUSE] Prüfe Polygon: landuse=forest
22:24:16.996 Capacitor/Console        I  File: https://localhost/overpass-landuse.js - Line 144 - Msg: [OVERPASS-LANDUSE] Punkt-in-Polygon: false (swapped: false) für Typ landuse=forest
22:24:16.997 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/pokemon-grid.js:176] Grid cell (2550,18678) maps to chain index 5 of 78 (rarity: common)
22:24:16.997 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:589] Accepted spawn in grid 5 (1/5)
22:24:16.997 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/pokemon-grid.js:176] Grid cell (2549,18679) maps to chain index 21 of 78 (rarity: common)
22:24:16.997 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:643] Found base Pokemon for chain 22: meowth (52)
22:24:16.998 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-manager.js:198] getTeamPokemon returning 6 Pokemon: Owei, Sichlor, Tragosso, Rossana, Evoli, arbok
22:24:16.998 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/spawn-levels.js:29] Average team level for spawns: 24 (from 6 Pokemon)
22:24:16.998 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/spawn-levels.js:94] Spawn level: 23 (team average 24 with deviation -1)
22:24:16.998 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon meowth with dex_number: null (from evolutionData.dex: undefined)
22:24:16.999 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 23 (common): 9733
22:24:16.999 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized meowth (Lvl 23) with 9733 XP
22:24:17.000 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:708] Starting parallel landuse data collection for 20 Pokemon with grid distribution...
22:24:17.000 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/utils/pokemon-utils.js:43] Found Pokemon by exact name match: meowth
22:24:17.000 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:428] Pokemon family for meowth (chain 22): meowth (Dex #52, Evo level: 28), persian (Dex #53, Evo level: null)
22:24:17.001 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:443] [getDisplayForm] Pokemon: id=mdhuc2g6hkhvis4, base_name=meowth, name=meowth, level=23
22:24:17.001 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:449] [getDisplayForm] Family: {name: meowth, dex: 52, evolution_level: 28}, {name: persian, dex: 53, evolution_level: none}
22:24:17.001 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:491] [getDisplayForm] Current stage: name=meowth, dex=52, evolution_level=28
22:24:17.004 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-manager.js:198] getTeamPokemon returning 6 Pokemon: Owei, Sichlor, Tragosso, Rossana, Evoli, arbok
22:24:17.004 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/spawn-levels.js:29] Average team level for spawns: 24 (from 6 Pokemon)
22:24:17.004 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/spawn-levels.js:94] Spawn level: 33 (team average 24 with deviation 9)
22:24:17.004 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon sandshrew with dex_number: null (from evolutionData.dex: undefined)
22:24:17.004 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 33 (common): 28749
22:24:17.005 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized sandshrew (Lvl 33) with 28749 XP
22:24:17.005 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/utils/pokemon-utils.js:43] Found Pokemon by exact name match: sandshrew
22:24:17.005 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:428] Pokemon family for sandshrew (chain 11): sandshrew (Dex #27, Evo level: 22), sandslash (Dex #28, Evo level: null)
22:24:17.005 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:443] [getDisplayForm] Pokemon: id=mdhuc2gc1sooeha, base_name=sandshrew, name=sandshrew, level=33
22:24:17.005 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:449] [getDisplayForm] Family: {name: sandshrew, dex: 27, evolution_level: 22}, {name: sandslash, dex: 28, evolution_level: none}
22:24:17.005 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:467] Evolution by level: sandshrew -> sandslash (Level: 33 >= 22)
22:24:17.005 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:491] [getDisplayForm] Current stage: name=sandslash, dex=28, evolution_level=none
22:24:17.006 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/pokemon-grid.js:176] Grid cell (2549,18680) maps to chain index 56 of 78 (rarity: rare)
22:24:17.006 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:740] Accepted landuse spawn in grid 56 (1/5)
22:24:17.006 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-manager.js:198] getTeamPokemon returning 6 Pokemon: Owei, Sichlor, Tragosso, Rossana, Evoli, arbok
22:24:17.006 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/spawn-levels.js:29] Average team level for spawns: 24 (from 6 Pokemon)
22:24:17.006 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/spawn-levels.js:94] Spawn level: 25 (team average 24 with deviation 1)
22:24:17.006 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon poliwrath with dex_number: null (from evolutionData.dex: undefined)
22:24:17.006 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 25 (common): 12500
22:24:17.007 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized poliwrath (Lvl 25) with 12500 XP
22:24:17.007 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/utils/pokemon-utils.js:43] Found Pokemon by exact name match: poliwrath
22:24:17.007 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:428] Pokemon family for poliwrath (chain 26): poliwag (Dex #60, Evo level: 25), poliwhirl (Dex #61, Evo level: 50), poliwrath (Dex #62, Evo level: null)
22:24:17.007 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:443] [getDisplayForm] Pokemon: id=mdhuc2ge4mxkowy, base_name=poliwrath, name=poliwrath, level=25
22:24:17.007 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:449] [getDisplayForm] Family: {name: poliwag, dex: 60, evolution_level: 25}, {name: poliwhirl, dex: 61, evolution_level: 50}, {name: poliwrath, dex: 62, evolution_level: none}
22:24:17.007 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:467] Evolution by level: poliwag -> poliwhirl (Level: 25 >= 25)
22:24:17.008 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:491] [getDisplayForm] Current stage: name=poliwhirl, dex=61, evolution_level=50
22:24:17.008 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/pokemon-grid.js:176] Grid cell (2550,18681) maps to chain index 51 of 78 (rarity: common)
22:24:17.008 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:740] Accepted landuse spawn in grid 51 (1/5)
22:24:17.008 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-manager.js:198] getTeamPokemon returning 6 Pokemon: Owei, Sichlor, Tragosso, Rossana, Evoli, arbok
22:24:17.008 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/spawn-levels.js:29] Average team level for spawns: 24 (from 6 Pokemon)
22:24:17.008 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/spawn-levels.js:80] Spawn level: 24 (exact match to team average)
22:24:17.008 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon hitmonlee with dex_number: null (from evolutionData.dex: undefined)
22:24:17.009 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 24 (common): 11059
22:24:17.009 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized hitmonlee (Lvl 24) with 11059 XP
22:24:17.009 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/utils/pokemon-utils.js:43] Found Pokemon by exact name match: hitmonlee
22:24:17.009 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:428] Pokemon family for hitmonlee (chain 47): hitmonlee (Dex #106, Evo level: null), hitmonchan (Dex #107, Evo level: null)
22:24:17.009 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:443] [getDisplayForm] Pokemon: id=mdhuc2gga5gibqo, base_name=hitmonlee, name=hitmonlee, level=24
22:24:17.009 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:449] [getDisplayForm] Family: {name: hitmonlee, dex: 106, evolution_level: none}, {name: hitmonchan, dex: 107, evolution_level: none}
22:24:17.010 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:491] [getDisplayForm] Current stage: name=hitmonlee, dex=106, evolution_level=none
22:24:17.010 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/pokemon-grid.js:176] Grid cell (2550,18678) maps to chain index 5 of 78 (rarity: common)
22:24:17.010 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:740] Accepted landuse spawn in grid 5 (1/5)
22:24:17.010 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-manager.js:198] getTeamPokemon returning 6 Pokemon: Owei, Sichlor, Tragosso, Rossana, Evoli, arbok
22:24:17.010 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/spawn-levels.js:29] Average team level for spawns: 24 (from 6 Pokemon)
22:24:17.010 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/spawn-levels.js:94] Spawn level: 5 (team average 24 with deviation -19)
22:24:17.010 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon primeape with dex_number: null (from evolutionData.dex: undefined)
22:24:17.011 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 5 (common): 100
22:24:17.011 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized primeape (Lvl 5) with 100 XP
22:24:17.011 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/utils/pokemon-utils.js:43] Found Pokemon by exact name match: primeape
22:24:17.011 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:428] Pokemon family for primeape (chain 24): mankey (Dex #56, Evo level: 28), primeape (Dex #57, Evo level: null)
22:24:17.011 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:443] [getDisplayForm] Pokemon: id=mdhuc2gikqv2v58, base_name=primeape, name=primeape, level=5
22:24:17.011 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:449] [getDisplayForm] Family: {name: mankey, dex: 56, evolution_level: 28}, {name: primeape, dex: 57, evolution_level: none}
22:24:17.011 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:491] [getDisplayForm] Current stage: name=mankey, dex=56, evolution_level=28
22:24:17.011 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/pokemon-grid.js:176] Grid cell (2550,18681) maps to chain index 51 of 78 (rarity: common)
22:24:17.012 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:740] Accepted landuse spawn in grid 51 (2/5)
22:24:17.012 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-manager.js:198] getTeamPokemon returning 6 Pokemon: Owei, Sichlor, Tragosso, Rossana, Evoli, arbok
22:24:17.012 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/spawn-levels.js:29] Average team level for spawns: 24 (from 6 Pokemon)
22:24:17.012 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/spawn-levels.js:94] Spawn level: 19 (team average 24 with deviation -5)
22:24:17.012 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon beedrill with dex_number: null (from evolutionData.dex: undefined)
22:24:17.012 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 19 (common): 5487
22:24:17.013 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized beedrill (Lvl 19) with 5487 XP
22:24:17.013 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/utils/pokemon-utils.js:43] Found Pokemon by exact name match: beedrill
22:24:17.013 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:428] Pokemon family for beedrill (chain 5): weedle (Dex #13, Evo level: 7), kakuna (Dex #14, Evo level: 10), beedrill (Dex #15, Evo level: null)
22:24:17.013 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:443] [getDisplayForm] Pokemon: id=mdhuc2gkwzznus8, base_name=beedrill, name=beedrill, level=19
22:24:17.013 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:449] [getDisplayForm] Family: {name: weedle, dex: 13, evolution_level: 7}, {name: kakuna, dex: 14, evolution_level: 10}, {name: beedrill, dex: 15, evolution_level: none}
22:24:17.014 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:467] Evolution by level: weedle -> kakuna (Level: 19 >= 7)
22:24:17.014 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:467] Evolution by level: kakuna -> beedrill (Level: 19 >= 10)
22:24:17.014 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:491] [getDisplayForm] Current stage: name=beedrill, dex=15, evolution_level=none
22:24:17.014 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/pokemon-grid.js:176] Grid cell (2551,18681) maps to chain index 43 of 78 (rarity: common)
22:24:17.014 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:740] Accepted landuse spawn in grid 43 (1/5)
22:24:17.014 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-manager.js:198] getTeamPokemon returning 6 Pokemon: Owei, Sichlor, Tragosso, Rossana, Evoli, arbok
22:24:17.014 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/spawn-levels.js:29] Average team level for spawns: 24 (from 6 Pokemon)
22:24:17.015 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/spawn-levels.js:94] Spawn level: 25 (team average 24 with deviation 1)
22:24:17.015 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon hitmonlee with dex_number: null (from evolutionData.dex: undefined)
22:24:17.015 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 25 (common): 12500
22:24:17.015 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized hitmonlee (Lvl 25) with 12500 XP
22:24:17.015 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/utils/pokemon-utils.js:43] Found Pokemon by exact name match: hitmonlee
22:24:17.015 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:428] Pokemon family for hitmonlee (chain 47): hitmonlee (Dex #106, Evo level: null), hitmonchan (Dex #107, Evo level: null)
22:24:17.015 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:443] [getDisplayForm] Pokemon: id=mdhuc2gmvnv66i9, base_name=hitmonlee, name=hitmonlee, level=25
22:24:17.015 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:449] [getDisplayForm] Family: {name: hitmonlee, dex: 106, evolution_level: none}, {name: hitmonchan, dex: 107, evolution_level: none}
22:24:17.016 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:491] [getDisplayForm] Current stage: name=hitmonlee, dex=106, evolution_level=none
22:24:17.016 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/pokemon-grid.js:176] Grid cell (2550,18678) maps to chain index 5 of 78 (rarity: common)
22:24:17.016 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:740] Accepted landuse spawn in grid 5 (2/5)
22:24:17.016 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-manager.js:198] getTeamPokemon returning 6 Pokemon: Owei, Sichlor, Tragosso, Rossana, Evoli, arbok
22:24:17.016 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/spawn-levels.js:29] Average team level for spawns: 24 (from 6 Pokemon)
22:24:17.016 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/spawn-levels.js:94] Spawn level: 17 (team average 24 with deviation -7)
22:24:17.016 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon sandslash with dex_number: null (from evolutionData.dex: undefined)
22:24:17.017 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 17 (common): 3930
22:24:17.017 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized sandslash (Lvl 17) with 3930 XP
22:24:17.017 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/utils/pokemon-utils.js:43] Found Pokemon by exact name match: sandslash
22:24:17.017 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:428] Pokemon family for sandslash (chain 11): sandshrew (Dex #27, Evo level: 22), sandslash (Dex #28, Evo level: null)
22:24:17.017 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:443] [getDisplayForm] Pokemon: id=mdhuc2go57hk3se, base_name=sandslash, name=sandslash, level=17
22:24:17.017 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:449] [getDisplayForm] Family: {name: sandshrew, dex: 27, evolution_level: 22}, {name: sandslash, dex: 28, evolution_level: none}
22:24:17.017 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:491] [getDisplayForm] Current stage: name=sandshrew, dex=27, evolution_level=22
22:24:17.019 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/pokemon-grid.js:176] Grid cell (2548,18680) maps to chain index 34 of 78 (rarity: common)
22:24:17.019 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:740] Accepted landuse spawn in grid 34 (1/5)
22:24:17.019 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-manager.js:198] getTeamPokemon returning 6 Pokemon: Owei, Sichlor, Tragosso, Rossana, Evoli, arbok
22:24:17.019 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/spawn-levels.js:29] Average team level for spawns: 24 (from 6 Pokemon)
22:24:17.019 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/spawn-levels.js:94] Spawn level: 25 (team average 24 with deviation 1)
22:24:17.020 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon golem with dex_number: null (from evolutionData.dex: undefined)
22:24:17.020 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 25 (common): 12500
22:24:17.020 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized golem (Lvl 25) with 12500 XP
22:24:17.020 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/utils/pokemon-utils.js:43] Found Pokemon by exact name match: golem
22:24:17.020 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:428] Pokemon family for golem (chain 31): geodude (Dex #74, Evo level: 25), graveler (Dex #75, Evo level: 50), golem (Dex #76, Evo level: null)
22:24:17.020 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:443] [getDisplayForm] Pokemon: id=mdhuc2grjnxc7tn, base_name=golem, name=golem, level=25
22:24:17.020 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:449] [getDisplayForm] Family: {name: geodude, dex: 74, evolution_level: 25}, {name: graveler, dex: 75, evolution_level: 50}, {name: golem, dex: 76, evolution_level: none}
22:24:17.020 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:467] Evolution by level: geodude -> graveler (Level: 25 >= 25)
22:24:17.020 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:491] [getDisplayForm] Current stage: name=graveler, dex=75, evolution_level=50
22:24:17.021 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/pokemon-grid.js:176] Grid cell (2549,18680) maps to chain index 56 of 78 (rarity: rare)
22:24:17.021 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:740] Accepted landuse spawn in grid 56 (2/5)
22:24:17.021 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-manager.js:198] getTeamPokemon returning 6 Pokemon: Owei, Sichlor, Tragosso, Rossana, Evoli, arbok
22:24:17.021 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/spawn-levels.js:29] Average team level for spawns: 24 (from 6 Pokemon)
22:24:17.021 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/spawn-levels.js:80] Spawn level: 24 (exact match to team average)
22:24:17.022 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon ditto with dex_number: null (from evolutionData.dex: undefined)
22:24:17.022 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 24 (common): 11059
22:24:17.022 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized ditto (Lvl 24) with 11059 XP
22:24:17.022 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/utils/pokemon-utils.js:43] Found Pokemon by exact name match: ditto
22:24:17.022 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:428] Pokemon family for ditto (chain 66): ditto (Dex #132, Evo level: null)
22:24:17.022 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:443] [getDisplayForm] Pokemon: id=mdhuc2gtokyq8r1, base_name=ditto, name=ditto, level=24
22:24:17.023 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:449] [getDisplayForm] Family: {name: ditto, dex: 132, evolution_level: none}
22:24:17.023 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:491] [getDisplayForm] Current stage: name=ditto, dex=132, evolution_level=none
22:24:17.023 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/pokemon-grid.js:176] Grid cell (2549,18678) maps to chain index 42 of 78 (rarity: common)
22:24:17.023 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:740] Accepted landuse spawn in grid 42 (1/5)
22:24:17.023 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-manager.js:198] getTeamPokemon returning 6 Pokemon: Owei, Sichlor, Tragosso, Rossana, Evoli, arbok
22:24:17.023 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/spawn-levels.js:29] Average team level for spawns: 24 (from 6 Pokemon)
22:24:17.023 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/spawn-levels.js:94] Spawn level: 39 (team average 24 with deviation 15)
22:24:17.024 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon mankey with dex_number: null (from evolutionData.dex: undefined)
22:24:17.024 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 39 (common): 47455
22:24:17.024 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized mankey (Lvl 39) with 47455 XP
22:24:17.024 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/utils/pokemon-utils.js:43] Found Pokemon by exact name match: mankey
22:24:17.024 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:428] Pokemon family for mankey (chain 24): mankey (Dex #56, Evo level: 28), primeape (Dex #57, Evo level: null)
22:24:17.024 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:443] [getDisplayForm] Pokemon: id=mdhuc2gvviiqba8, base_name=mankey, name=mankey, level=39
22:24:17.024 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:449] [getDisplayForm] Family: {name: mankey, dex: 56, evolution_level: 28}, {name: primeape, dex: 57, evolution_level: none}
22:24:17.025 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:467] Evolution by level: mankey -> primeape (Level: 39 >= 28)
22:24:17.025 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:491] [getDisplayForm] Current stage: name=primeape, dex=57, evolution_level=none
22:24:17.025 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/pokemon-grid.js:176] Grid cell (2550,18681) maps to chain index 51 of 78 (rarity: common)
22:24:17.025 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:740] Accepted landuse spawn in grid 51 (3/5)
22:24:17.025 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-manager.js:198] getTeamPokemon returning 6 Pokemon: Owei, Sichlor, Tragosso, Rossana, Evoli, arbok
22:24:17.025 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/spawn-levels.js:29] Average team level for spawns: 24 (from 6 Pokemon)
22:24:17.025 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/spawn-levels.js:94] Spawn level: 30 (team average 24 with deviation 6)
22:24:17.026 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon poliwrath with dex_number: null (from evolutionData.dex: undefined)
22:24:17.026 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 30 (common): 21600
22:24:17.026 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized poliwrath (Lvl 30) with 21600 XP
22:24:17.026 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/utils/pokemon-utils.js:43] Found Pokemon by exact name match: poliwrath
22:24:17.026 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:428] Pokemon family for poliwrath (chain 26): poliwag (Dex #60, Evo level: 25), poliwhirl (Dex #61, Evo level: 50), poliwrath (Dex #62, Evo level: null)
22:24:17.026 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:443] [getDisplayForm] Pokemon: id=mdhuc2gx4e744hb, base_name=poliwrath, name=poliwrath, level=30
22:24:17.026 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:449] [getDisplayForm] Family: {name: poliwag, dex: 60, evolution_level: 25}, {name: poliwhirl, dex: 61, evolution_level: 50}, {name: poliwrath, dex: 62, evolution_level: none}
22:24:17.027 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:467] Evolution by level: poliwag -> poliwhirl (Level: 30 >= 25)
22:24:17.027 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:491] [getDisplayForm] Current stage: name=poliwhirl, dex=61, evolution_level=50
22:24:17.027 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/pokemon-grid.js:176] Grid cell (2550,18678) maps to chain index 5 of 78 (rarity: common)
22:24:17.027 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:740] Accepted landuse spawn in grid 5 (3/5)
22:24:17.027 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-manager.js:198] getTeamPokemon returning 6 Pokemon: Owei, Sichlor, Tragosso, Rossana, Evoli, arbok
22:24:17.027 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/spawn-levels.js:29] Average team level for spawns: 24 (from 6 Pokemon)
22:24:17.027 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/spawn-levels.js:94] Spawn level: 23 (team average 24 with deviation -1)
22:24:17.027 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon golem with dex_number: null (from evolutionData.dex: undefined)
22:24:17.028 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 23 (common): 9733
22:24:17.028 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized golem (Lvl 23) with 9733 XP
22:24:17.028 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/utils/pokemon-utils.js:43] Found Pokemon by exact name match: golem
22:24:17.028 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:428] Pokemon family for golem (chain 31): geodude (Dex #74, Evo level: 25), graveler (Dex #75, Evo level: 50), golem (Dex #76, Evo level: null)
22:24:17.028 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:443] [getDisplayForm] Pokemon: id=mdhuc2gz6dydn0w, base_name=golem, name=golem, level=23
22:24:17.028 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:449] [getDisplayForm] Family: {name: geodude, dex: 74, evolution_level: 25}, {name: graveler, dex: 75, evolution_level: 50}, {name: golem, dex: 76, evolution_level: none}
22:24:17.028 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:491] [getDisplayForm] Current stage: name=geodude, dex=74, evolution_level=25
22:24:17.029 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/pokemon-grid.js:176] Grid cell (2549,18680) maps to chain index 56 of 78 (rarity: rare)
22:24:17.029 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:740] Accepted landuse spawn in grid 56 (3/5)
22:24:17.029 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-manager.js:198] getTeamPokemon returning 6 Pokemon: Owei, Sichlor, Tragosso, Rossana, Evoli, arbok
22:24:17.029 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/spawn-levels.js:29] Average team level for spawns: 24 (from 6 Pokemon)
22:24:17.029 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/spawn-levels.js:94] Spawn level: 10 (team average 24 with deviation -14)
22:24:17.029 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon hitmonlee with dex_number: null (from evolutionData.dex: undefined)
22:24:17.030 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 10 (common): 800
22:24:17.030 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized hitmonlee (Lvl 10) with 800 XP
22:24:17.030 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/utils/pokemon-utils.js:43] Found Pokemon by exact name match: hitmonlee
22:24:17.030 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:428] Pokemon family for hitmonlee (chain 47): hitmonlee (Dex #106, Evo level: null), hitmonchan (Dex #107, Evo level: null)
22:24:17.030 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:443] [getDisplayForm] Pokemon: id=mdhuc2h176sqtjs, base_name=hitmonlee, name=hitmonlee, level=10
22:24:17.030 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:449] [getDisplayForm] Family: {name: hitmonlee, dex: 106, evolution_level: none}, {name: hitmonchan, dex: 107, evolution_level: none}
22:24:17.030 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:491] [getDisplayForm] Current stage: name=hitmonlee, dex=106, evolution_level=none
22:24:17.031 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/pokemon-grid.js:176] Grid cell (2550,18678) maps to chain index 5 of 78 (rarity: common)
22:24:17.032 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:740] Accepted landuse spawn in grid 5 (4/5)
22:24:17.032 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-manager.js:198] getTeamPokemon returning 6 Pokemon: Owei, Sichlor, Tragosso, Rossana, Evoli, arbok
22:24:17.032 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/spawn-levels.js:29] Average team level for spawns: 24 (from 6 Pokemon)
22:24:17.032 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/spawn-levels.js:94] Spawn level: 10 (team average 24 with deviation -14)
22:24:17.032 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon primeape with dex_number: null (from evolutionData.dex: undefined)
22:24:17.033 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 10 (common): 800
22:24:17.033 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized primeape (Lvl 10) with 800 XP
22:24:17.033 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/utils/pokemon-utils.js:43] Found Pokemon by exact name match: primeape
22:24:17.033 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:428] Pokemon family for primeape (chain 24): mankey (Dex #56, Evo level: 28), primeape (Dex #57, Evo level: null)
22:24:17.033 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:443] [getDisplayForm] Pokemon: id=mdhuc2h35nc1aj8, base_name=primeape, name=primeape, level=10
22:24:17.033 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:449] [getDisplayForm] Family: {name: mankey, dex: 56, evolution_level: 28}, {name: primeape, dex: 57, evolution_level: none}
22:24:17.033 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:491] [getDisplayForm] Current stage: name=mankey, dex=56, evolution_level=28
22:24:17.033 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/pokemon-grid.js:176] Grid cell (2550,18678) maps to chain index 5 of 78 (rarity: common)
22:24:17.033 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:740] Accepted landuse spawn in grid 5 (5/5)
22:24:17.034 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-manager.js:198] getTeamPokemon returning 6 Pokemon: Owei, Sichlor, Tragosso, Rossana, Evoli, arbok
22:24:17.034 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/spawn-levels.js:29] Average team level for spawns: 24 (from 6 Pokemon)
22:24:17.034 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/spawn-levels.js:94] Spawn level: 25 (team average 24 with deviation 1)
22:24:17.034 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon doduo with dex_number: null (from evolutionData.dex: undefined)
22:24:17.034 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 25 (common): 12500
22:24:17.034 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized doduo (Lvl 25) with 12500 XP
22:24:17.034 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/utils/pokemon-utils.js:43] Found Pokemon by exact name match: doduo
22:24:17.034 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:428] Pokemon family for doduo (chain 36): doduo (Dex #84, Evo level: 31), dodrio (Dex #85, Evo level: null)
22:24:17.034 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:443] [getDisplayForm] Pokemon: id=mdhuc2h5mgd96ql, base_name=doduo, name=doduo, level=25
22:24:17.035 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:449] [getDisplayForm] Family: {name: doduo, dex: 84, evolution_level: 31}, {name: dodrio, dex: 85, evolution_level: none}
22:24:17.035 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:491] [getDisplayForm] Current stage: name=doduo, dex=84, evolution_level=31
22:24:17.035 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/pokemon-grid.js:176] Grid cell (2549,18678) maps to chain index 42 of 78 (rarity: common)
22:24:17.035 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:740] Accepted landuse spawn in grid 42 (2/5)
22:24:17.035 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-manager.js:198] getTeamPokemon returning 6 Pokemon: Owei, Sichlor, Tragosso, Rossana, Evoli, arbok
22:24:17.035 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/spawn-levels.js:29] Average team level for spawns: 24 (from 6 Pokemon)
22:24:17.035 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/spawn-levels.js:94] Spawn level: 23 (team average 24 with deviation -1)
22:24:17.035 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon scyther with dex_number: null (from evolutionData.dex: undefined)
22:24:17.035 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 23 (common): 9733
22:24:17.036 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized scyther (Lvl 23) with 9733 XP
22:24:17.036 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/utils/pokemon-utils.js:43] Found Pokemon by exact name match: scyther
22:24:17.036 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:428] Pokemon family for scyther (chain 58): scyther (Dex #123, Evo level: null)
22:24:17.036 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:443] [getDisplayForm] Pokemon: id=mdhuc2h61dilq9p, base_name=scyther, name=scyther, level=23
22:24:17.036 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:449] [getDisplayForm] Family: {name: scyther, dex: 123, evolution_level: none}
22:24:17.036 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:491] [getDisplayForm] Current stage: name=scyther, dex=123, evolution_level=none
22:24:17.036 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/pokemon-grid.js:176] Grid cell (2552,18677) maps to chain index 40 of 78 (rarity: common)
22:24:17.036 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:740] Accepted landuse spawn in grid 40 (1/5)
22:24:17.036 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-manager.js:198] getTeamPokemon returning 6 Pokemon: Owei, Sichlor, Tragosso, Rossana, Evoli, arbok
22:24:17.037 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/spawn-levels.js:29] Average team level for spawns: 24 (from 6 Pokemon)
22:24:17.037 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/spawn-levels.js:80] Spawn level: 24 (exact match to team average)
22:24:17.037 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon haunter with dex_number: null (from evolutionData.dex: undefined)
22:24:17.037 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 24 (common): 11059
22:24:17.037 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized haunter (Lvl 24) with 11059 XP
22:24:17.037 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/utils/pokemon-utils.js:43] Found Pokemon by exact name match: haunter
22:24:17.037 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:428] Pokemon family for haunter (chain 40): gastly (Dex #92, Evo level: 25), haunter (Dex #93, Evo level: 50), gengar (Dex #94, Evo level: null)
22:24:17.037 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:443] [getDisplayForm] Pokemon: id=mdhuc2h8skk6kpy, base_name=haunter, name=haunter, level=24
22:24:17.038 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:449] [getDisplayForm] Family: {name: gastly, dex: 92, evolution_level: 25}, {name: haunter, dex: 93, evolution_level: 50}, {name: gengar, dex: 94, evolution_level: none}
22:24:17.038 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:491] [getDisplayForm] Current stage: name=gastly, dex=92, evolution_level=25
22:24:17.038 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/pokemon-grid.js:176] Grid cell (2549,18679) maps to chain index 21 of 78 (rarity: common)
22:24:17.038 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:740] Accepted landuse spawn in grid 21 (1/5)
22:24:17.038 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-manager.js:198] getTeamPokemon returning 6 Pokemon: Owei, Sichlor, Tragosso, Rossana, Evoli, arbok
22:24:17.038 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/spawn-levels.js:29] Average team level for spawns: 24 (from 6 Pokemon)
22:24:17.038 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/spawn-levels.js:94] Spawn level: 10 (team average 24 with deviation -14)
22:24:17.038 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon hitmonchan with dex_number: null (from evolutionData.dex: undefined)
22:24:17.039 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 10 (common): 800
22:24:17.039 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized hitmonchan (Lvl 10) with 800 XP
22:24:17.039 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/utils/pokemon-utils.js:43] Found Pokemon by exact name match: hitmonchan
22:24:17.039 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:428] Pokemon family for hitmonchan (chain 47): hitmonlee (Dex #106, Evo level: null), hitmonchan (Dex #107, Evo level: null)
22:24:17.039 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:443] [getDisplayForm] Pokemon: id=mdhuc2ha7uqkqjn, base_name=hitmonchan, name=hitmonchan, level=10
22:24:17.039 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:449] [getDisplayForm] Family: {name: hitmonlee, dex: 106, evolution_level: none}, {name: hitmonchan, dex: 107, evolution_level: none}
22:24:17.039 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:491] [getDisplayForm] Current stage: name=hitmonlee, dex=106, evolution_level=none
22:24:17.040 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/pokemon-grid.js:176] Grid cell (2550,18681) maps to chain index 51 of 78 (rarity: common)
22:24:17.040 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:740] Accepted landuse spawn in grid 51 (4/5)
22:24:17.040 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-manager.js:198] getTeamPokemon returning 6 Pokemon: Owei, Sichlor, Tragosso, Rossana, Evoli, arbok
22:24:17.040 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/spawn-levels.js:29] Average team level for spawns: 24 (from 6 Pokemon)
22:24:17.040 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/spawn-levels.js:94] Spawn level: 23 (team average 24 with deviation -1)
22:24:17.040 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon onix with dex_number: null (from evolutionData.dex: undefined)
22:24:17.040 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 23 (common): 9733
22:24:17.041 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized onix (Lvl 23) with 9733 XP
22:24:17.041 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/utils/pokemon-utils.js:43] Found Pokemon by exact name match: onix
22:24:17.041 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:428] Pokemon family for onix (chain 41): onix (Dex #95, Evo level: null)
22:24:17.041 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:443] [getDisplayForm] Pokemon: id=mdhuc2hctmwdge0, base_name=onix, name=onix, level=23
22:24:17.041 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:449] [getDisplayForm] Family: {name: onix, dex: 95, evolution_level: none}
22:24:17.041 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:491] [getDisplayForm] Current stage: name=onix, dex=95, evolution_level=none
22:24:17.041 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/pokemon-grid.js:176] Grid cell (2548,18680) maps to chain index 34 of 78 (rarity: common)
22:24:17.041 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:740] Accepted landuse spawn in grid 34 (2/5)
22:24:17.042 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-manager.js:198] getTeamPokemon returning 6 Pokemon: Owei, Sichlor, Tragosso, Rossana, Evoli, arbok
22:24:17.042 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/spawn-levels.js:29] Average team level for spawns: 24 (from 6 Pokemon)
22:24:17.042 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/spawn-levels.js:94] Spawn level: 19 (team average 24 with deviation -5)
22:24:17.042 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon poliwrath with dex_number: null (from evolutionData.dex: undefined)
22:24:17.042 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 19 (common): 5487
22:24:17.042 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized poliwrath (Lvl 19) with 5487 XP
22:24:17.042 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/utils/pokemon-utils.js:43] Found Pokemon by exact name match: poliwrath
22:24:17.043 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:428] Pokemon family for poliwrath (chain 26): poliwag (Dex #60, Evo level: 25), poliwhirl (Dex #61, Evo level: 50), poliwrath (Dex #62, Evo level: null)
22:24:17.043 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:443] [getDisplayForm] Pokemon: id=mdhuc2hedbofdsb, base_name=poliwrath, name=poliwrath, level=19
22:24:17.043 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:449] [getDisplayForm] Family: {name: poliwag, dex: 60, evolution_level: 25}, {name: poliwhirl, dex: 61, evolution_level: 50}, {name: poliwrath, dex: 62, evolution_level: none}
22:24:17.043 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:491] [getDisplayForm] Current stage: name=poliwag, dex=60, evolution_level=25
22:24:17.043 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/pokemon-grid.js:176] Grid cell (2550,18681) maps to chain index 51 of 78 (rarity: common)
22:24:17.043 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:740] Accepted landuse spawn in grid 51 (5/5)
22:24:17.043 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:758] Landuse grid distribution summary: 8 cells used, max per cell: 5
22:24:17.044 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:760]   Landuse Grid 56: 3 Pokemon
22:24:17.044 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:760]   Landuse Grid 51: 5 Pokemon
22:24:17.044 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:760]   Landuse Grid 5: 5 Pokemon
22:24:17.044 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:760]   Landuse Grid 43: 1 Pokemon
22:24:17.044 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:760]   Landuse Grid 34: 2 Pokemon
22:24:17.044 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:760]   Landuse Grid 42: 2 Pokemon
22:24:17.044 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:760]   Landuse Grid 40: 1 Pokemon
22:24:17.044 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:760]   Landuse Grid 21: 1 Pokemon
22:24:17.044 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon sandshrew with dex_number: null (from evolutionData.dex: undefined)
22:24:17.045 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 33 (common): 28749
22:24:17.045 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized sandshrew (Lvl 33) with 28749 XP
22:24:17.045 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:391] Created final Pokemon: sandslash (base: sandshrew) Level 33, Dex #28 [Landuse Special]
22:24:17.045 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/capacitor/time-events.js:218] Syncing 1 Pokemon with storage using complete data
22:24:17.045 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/storage/storage-service.js:47] Saved to storage: timeEventSpawns
22:24:17.046 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon poliwrath with dex_number: null (from evolutionData.dex: undefined)
22:24:17.046 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 25 (common): 12500
22:24:17.046 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized poliwrath (Lvl 25) with 12500 XP
22:24:17.046 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:391] Created final Pokemon: poliwhirl (base: poliwrath) Level 25, Dex #61 [Landuse Special]
22:24:17.046 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/capacitor/time-events.js:218] Syncing 2 Pokemon with storage using complete data
22:24:17.047 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/storage/storage-service.js:47] Saved to storage: timeEventSpawns
22:24:17.047 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon hitmonlee with dex_number: null (from evolutionData.dex: undefined)
22:24:17.047 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 24 (common): 11059
22:24:17.047 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized hitmonlee (Lvl 24) with 11059 XP
22:24:17.047 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:391] Created final Pokemon: hitmonlee (base: hitmonlee) Level 24, Dex #106 [Landuse Special]
22:24:17.047 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/capacitor/time-events.js:218] Syncing 3 Pokemon with storage using complete data
22:24:17.047 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/storage/storage-service.js:47] Saved to storage: timeEventSpawns
22:24:17.048 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon primeape with dex_number: null (from evolutionData.dex: undefined)
22:24:17.048 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 5 (common): 100
22:24:17.048 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized primeape (Lvl 5) with 100 XP
22:24:17.048 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:391] Created final Pokemon: mankey (base: primeape) Level 5, Dex #56 [Landuse Special]
22:24:17.048 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/capacitor/time-events.js:218] Syncing 4 Pokemon with storage using complete data
22:24:17.048 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/storage/storage-service.js:47] Saved to storage: timeEventSpawns
22:24:17.048 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon beedrill with dex_number: null (from evolutionData.dex: undefined)
22:24:17.048 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 19 (common): 5487
22:24:17.049 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized beedrill (Lvl 19) with 5487 XP
22:24:17.049 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:391] Created final Pokemon: beedrill (base: beedrill) Level 19, Dex #15 [Landuse Special]
22:24:17.049 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/capacitor/time-events.js:218] Syncing 5 Pokemon with storage using complete data
22:24:17.049 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/storage/storage-service.js:47] Saved to storage: timeEventSpawns
22:24:17.049 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon hitmonlee with dex_number: null (from evolutionData.dex: undefined)
22:24:17.049 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 25 (common): 12500
22:24:17.049 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized hitmonlee (Lvl 25) with 12500 XP
22:24:17.050 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:391] Created final Pokemon: hitmonlee (base: hitmonlee) Level 25, Dex #106 [Landuse Special]
22:24:17.050 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/capacitor/time-events.js:218] Syncing 6 Pokemon with storage using complete data
22:24:17.050 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/storage/storage-service.js:47] Saved to storage: timeEventSpawns
22:24:17.050 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon sandslash with dex_number: null (from evolutionData.dex: undefined)
22:24:17.050 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 17 (common): 3930
22:24:17.050 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized sandslash (Lvl 17) with 3930 XP
22:24:17.050 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:391] Created final Pokemon: sandshrew (base: sandslash) Level 17, Dex #27 [Landuse Special]
22:24:17.050 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/capacitor/time-events.js:218] Syncing 7 Pokemon with storage using complete data
22:24:17.050 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/storage/storage-service.js:47] Saved to storage: timeEventSpawns
22:24:17.051 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon golem with dex_number: null (from evolutionData.dex: undefined)
22:24:17.051 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 25 (common): 12500
22:24:17.051 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized golem (Lvl 25) with 12500 XP
22:24:17.051 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:391] Created final Pokemon: graveler (base: golem) Level 25, Dex #75 [Landuse Special]
22:24:17.051 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/capacitor/time-events.js:218] Syncing 8 Pokemon with storage using complete data
22:24:17.051 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/storage/storage-service.js:47] Saved to storage: timeEventSpawns
22:24:17.051 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon ditto with dex_number: null (from evolutionData.dex: undefined)
22:24:17.052 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 24 (common): 11059
22:24:17.052 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized ditto (Lvl 24) with 11059 XP
22:24:17.052 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:391] Created final Pokemon: ditto (base: ditto) Level 24, Dex #132 [Landuse Special]
22:24:17.052 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/capacitor/time-events.js:218] Syncing 9 Pokemon with storage using complete data
22:24:17.052 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/storage/storage-service.js:47] Saved to storage: timeEventSpawns
22:24:17.052 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon mankey with dex_number: null (from evolutionData.dex: undefined)
22:24:17.052 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 39 (common): 47455
22:24:17.053 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized mankey (Lvl 39) with 47455 XP
22:24:17.053 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:391] Created final Pokemon: primeape (base: mankey) Level 39, Dex #57 [Landuse Special]
22:24:17.053 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/capacitor/time-events.js:218] Syncing 10 Pokemon with storage using complete data
22:24:17.053 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/storage/storage-service.js:47] Saved to storage: timeEventSpawns
22:24:17.053 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon poliwrath with dex_number: null (from evolutionData.dex: undefined)
22:24:17.053 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 30 (common): 21600
22:24:17.053 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized poliwrath (Lvl 30) with 21600 XP
22:24:17.053 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:391] Created final Pokemon: poliwhirl (base: poliwrath) Level 30, Dex #61 [Landuse Special]
22:24:17.054 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/capacitor/time-events.js:218] Syncing 11 Pokemon with storage using complete data
22:24:17.054 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/storage/storage-service.js:47] Saved to storage: timeEventSpawns
22:24:17.054 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon golem with dex_number: null (from evolutionData.dex: undefined)
22:24:17.054 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 23 (common): 9733
22:24:17.054 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized golem (Lvl 23) with 9733 XP
22:24:17.054 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:391] Created final Pokemon: geodude (base: golem) Level 23, Dex #74 [Landuse Special]
22:24:17.054 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/capacitor/time-events.js:218] Syncing 12 Pokemon with storage using complete data
22:24:17.054 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/storage/storage-service.js:47] Saved to storage: timeEventSpawns
22:24:17.055 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon hitmonlee with dex_number: null (from evolutionData.dex: undefined)
22:24:17.055 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 10 (common): 800
22:24:17.055 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized hitmonlee (Lvl 10) with 800 XP
22:24:17.055 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:391] Created final Pokemon: hitmonlee (base: hitmonlee) Level 10, Dex #106 [Landuse Special]
22:24:17.055 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/capacitor/time-events.js:218] Syncing 13 Pokemon with storage using complete data
22:24:17.055 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/storage/storage-service.js:47] Saved to storage: timeEventSpawns
22:24:17.055 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon primeape with dex_number: null (from evolutionData.dex: undefined)
22:24:17.055 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 10 (common): 800
22:24:17.056 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized primeape (Lvl 10) with 800 XP
22:24:17.056 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:391] Created final Pokemon: mankey (base: primeape) Level 10, Dex #56 [Landuse Special]
22:24:17.056 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/capacitor/time-events.js:218] Syncing 14 Pokemon with storage using complete data
22:24:17.056 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/storage/storage-service.js:47] Saved to storage: timeEventSpawns
22:24:17.056 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon doduo with dex_number: null (from evolutionData.dex: undefined)
22:24:17.056 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 25 (common): 12500
22:24:17.056 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized doduo (Lvl 25) with 12500 XP
22:24:17.056 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:391] Created final Pokemon: doduo (base: doduo) Level 25, Dex #84 [Landuse Special]
22:24:17.057 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/capacitor/time-events.js:218] Syncing 15 Pokemon with storage using complete data
22:24:17.057 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/storage/storage-service.js:47] Saved to storage: timeEventSpawns
22:24:17.057 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon scyther with dex_number: null (from evolutionData.dex: undefined)
22:24:17.057 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 23 (common): 9733
22:24:17.057 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized scyther (Lvl 23) with 9733 XP
22:24:17.058 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:391] Created final Pokemon: scyther (base: scyther) Level 23, Dex #123 [Landuse Special]
22:24:17.058 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/capacitor/time-events.js:218] Syncing 16 Pokemon with storage using complete data
22:24:17.058 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/storage/storage-service.js:47] Saved to storage: timeEventSpawns
22:24:17.058 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon haunter with dex_number: null (from evolutionData.dex: undefined)
22:24:17.058 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 24 (common): 11059
22:24:17.058 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized haunter (Lvl 24) with 11059 XP
22:24:17.058 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:391] Created final Pokemon: gastly (base: haunter) Level 24, Dex #92 [Landuse Special]
22:24:17.058 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/capacitor/time-events.js:218] Syncing 17 Pokemon with storage using complete data
22:24:17.058 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/storage/storage-service.js:47] Saved to storage: timeEventSpawns
22:24:17.059 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon hitmonchan with dex_number: null (from evolutionData.dex: undefined)
22:24:17.059 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 10 (common): 800
22:24:17.059 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized hitmonchan (Lvl 10) with 800 XP
22:24:17.059 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:391] Created final Pokemon: hitmonlee (base: hitmonchan) Level 10, Dex #106 [Landuse Special]
22:24:17.059 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/capacitor/time-events.js:218] Syncing 18 Pokemon with storage using complete data
22:24:17.059 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/storage/storage-service.js:47] Saved to storage: timeEventSpawns
22:24:17.059 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon onix with dex_number: null (from evolutionData.dex: undefined)
22:24:17.059 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 23 (common): 9733
22:24:17.060 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized onix (Lvl 23) with 9733 XP
22:24:17.060 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:391] Created final Pokemon: onix (base: onix) Level 23, Dex #95 [Landuse Special]
22:24:17.060 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/capacitor/time-events.js:218] Syncing 19 Pokemon with storage using complete data
22:24:17.060 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/storage/storage-service.js:47] Saved to storage: timeEventSpawns
22:24:17.060 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon poliwrath with dex_number: null (from evolutionData.dex: undefined)
22:24:17.060 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 19 (common): 5487
22:24:17.060 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized poliwrath (Lvl 19) with 5487 XP
22:24:17.061 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:391] Created final Pokemon: poliwag (base: poliwrath) Level 19, Dex #60 [Landuse Special]
22:24:17.061 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/capacitor/time-events.js:218] Syncing 20 Pokemon with storage using complete data
22:24:17.061 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/storage/storage-service.js:47] Saved to storage: timeEventSpawns
22:24:17.061 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:775] Successfully created 20 landuse Pokemon with isolated spawning system
22:24:17.066 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/main.js:202] Synced 20 Pokemon with storage after update
22:24:17.066 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/main.js:202] Synced 20 Pokemon with storage after update
22:24:17.067 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/main.js:202] Synced 20 Pokemon with storage after update
22:24:17.067 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/main.js:202] Synced 20 Pokemon with storage after update
22:24:17.067 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/main.js:202] Synced 20 Pokemon with storage after update
22:24:17.067 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/main.js:202] Synced 20 Pokemon with storage after update
22:24:17.067 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/main.js:202] Synced 20 Pokemon with storage after update
22:24:17.067 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/main.js:202] Synced 20 Pokemon with storage after update
22:24:17.067 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/main.js:202] Synced 20 Pokemon with storage after update
22:24:17.068 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/main.js:202] Synced 20 Pokemon with storage after update
22:24:17.068 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/main.js:202] Synced 20 Pokemon with storage after update
22:24:17.068 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/main.js:202] Synced 20 Pokemon with storage after update
22:24:17.068 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/main.js:202] Synced 20 Pokemon with storage after update
22:24:17.068 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/main.js:202] Synced 20 Pokemon with storage after update
22:24:17.068 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/main.js:202] Synced 20 Pokemon with storage after update
22:24:17.068 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/main.js:202] Synced 20 Pokemon with storage after update
22:24:17.068 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/main.js:202] Synced 20 Pokemon with storage after update
22:24:17.068 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/main.js:202] Synced 20 Pokemon with storage after update
22:24:17.069 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/main.js:202] Synced 20 Pokemon with storage after update
22:24:17.069 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/main.js:202] Synced 20 Pokemon with storage after update
22:24:17.070 Capacitor                D  Handling local request: https://localhost/src/PokemonSprites/28.png
22:24:17.070 Capacitor                D  Handling local request: https://localhost/src/PokemonSprites/61.png
22:24:17.070 Capacitor                D  Handling local request: https://localhost/src/PokemonSprites/106.png
22:24:17.070 Capacitor                D  Handling local request: https://localhost/src/PokemonSprites/56.png
22:24:17.072 Capacitor                D  Handling local request: https://localhost/src/PokemonSprites/15.png
22:24:17.072 Capacitor                D  Handling local request: https://localhost/src/PokemonSprites/27.png
22:24:17.073 Capacitor                D  Handling local request: https://localhost/src/PokemonSprites/75.png
22:24:17.073 Capacitor                D  Handling local request: https://localhost/src/PokemonSprites/132.png
22:24:17.074 Capacitor                D  Handling local request: https://localhost/src/PokemonSprites/57.png
22:24:17.075 Capacitor                D  Handling local request: https://localhost/src/PokemonSprites/84.png
22:24:17.075 Capacitor                D  Handling local request: https://localhost/src/PokemonSprites/74.png
22:24:17.076 Capacitor                D  Handling local request: https://localhost/src/PokemonSprites/123.png
22:24:17.076 Capacitor                D  Handling local request: https://localhost/src/PokemonSprites/92.png
22:24:17.077 Capacitor                D  Handling local request: https://localhost/src/PokemonSprites/95.png
22:24:17.077 Capacitor                D  Handling local request: https://localhost/src/PokemonSprites/60.png
22:24:19.993 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/capacitor/geolocation.js:96] Capacitor Geolocation update: lat=50.4859795, lng=10.8340465, accuracy=15.489
22:24:19.994 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/main.js:444] Updating player sprite at 50.4859795, 10.8340465, heading: 270
22:24:19.994 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/main.js:457] Player is static, distSinceLastMove: 0.38870186126616335
22:24:22.364 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/capacitor/geolocation.js:96] Capacitor Geolocation update: lat=50.4859794, lng=10.8340466, accuracy=15.583
22:24:22.365 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/main.js:444] Updating player sprite at 50.4859794, 10.8340466, heading: 270
22:24:22.365 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/main.js:457] Player is static, distSinceLastMove: 0.39134861694146844
22:24:29.933 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/capacitor/geolocation.js:96] Capacitor Geolocation update: lat=50.4859815, lng=10.8340462, accuracy=15.309
22:24:29.933 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/main.js:444] Updating player sprite at 50.4859815, 10.8340462, heading: 270
22:24:29.933 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/main.js:457] Player is static, distSinceLastMove: 0.4999744192598937
22:24:32.361 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/capacitor/geolocation.js:96] Capacitor Geolocation update: lat=50.4859816, lng=10.8340461, accuracy=15.253
22:24:32.361 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/main.js:444] Updating player sprite at 50.4859816, 10.8340461, heading: 270
22:24:32.361 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/main.js:457] Player is static, distSinceLastMove: 0.5034910817780648
22:24:40.262 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/capacitor/geolocation.js:96] Capacitor Geolocation update: lat=50.4859814, lng=10.8340483, accuracy=15.194
22:24:40.263 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/main.js:444] Updating player sprite at 50.4859814, 10.8340483, heading: 270
22:24:40.264 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/main.js:457] Player is static, distSinceLastMove: 0.6040886696116176
22:24:41.263 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/pokemon-grid.js:176] Grid cell (2549,18679) maps to chain index 21 of 78 (rarity: common)
22:24:41.263 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:589] Accepted spawn in grid 21 (1/5)
22:24:41.265 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/pokemon-grid.js:176] Grid cell (2549,18680) maps to chain index 56 of 78 (rarity: rare)
22:24:41.265 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-spawner.js:643] Found base Pokemon for chain 57: mr-mime (122)
22:24:41.265 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/pokemon-manager.js:198] getTeamPokemon returning 6 Pokemon: Owei, Sichlor, Tragosso, Rossana, Evoli, arbok
22:24:41.266 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/spawn-levels.js:29] Average team level for spawns: 24 (from 6 Pokemon)
22:24:41.266 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/spawn-levels.js:94] Spawn level: 26 (team average 24 with deviation 2)
22:24:41.267 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:31] Creating new Pokemon mr-mime with dex_number: null (from evolutionData.dex: undefined)
22:24:41.267 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/services/experience-system.js:218] Initial XP for level 26 (common): 14060
22:24:41.267 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:51] Initialized mr-mime (Lvl 26) with 14060 XP
22:24:41.269 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/utils/pokemon-utils.js:43] Found Pokemon by exact name match: mr-mime
22:24:41.269 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:428] Pokemon family for mr-mime (chain 57): mr-mime (Dex #122, Evo level: null)
22:24:41.270 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:443] [getDisplayForm] Pokemon: id=mdhucl6afp6aijw, base_name=mr-mime, name=mr-mime, level=26
22:24:41.270 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:449] [getDisplayForm] Family: {name: mr-mime, dex: 122, evolution_level: none}
22:24:41.270 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/Pokemon.js:491] [getDisplayForm] Current stage: name=mr-mime, dex=122, evolution_level=none
22:24:42.367 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/capacitor/geolocation.js:96] Capacitor Geolocation update: lat=50.4859813, lng=10.8340485, accuracy=15.324
22:24:42.367 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/main.js:444] Updating player sprite at 50.4859813, 10.8340485, heading: 270
22:24:42.368 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/main.js:457] Player is static, distSinceLastMove: 0.6092200315395845
22:24:49.993 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/capacitor/geolocation.js:96] Capacitor Geolocation update: lat=50.4859825, lng=10.8340386, accuracy=21.013
22:24:49.994 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/main.js:444] Updating player sprite at 50.4859825, 10.8340386, heading: 270
22:24:49.994 Capacitor/Console        D  File: https://localhost/utils/logger.js - Line 109 - Msg: [https://localhost/main.js:457] Player is static, distSinceLastMove: 0.5175513116997424
